#!/usr/bin/env python
"""
Test script to verify that new recurring mentions are automatically approved.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')
django.setup()

from django.utils import timezone
from django.contrib.auth.models import User
from datetime import date, time, timedelta
from apps.mentions.models import RecurringMention, RecurringMentionShow, Mention
from apps.core.models import Client
from apps.shows.models import Show
from apps.settings.models import OrganizationSettings

def test_new_recurring_mention_approval():
    """Test that new recurring mentions are automatically approved."""
    print("=== Testing New Recurring Mention Auto-Approval ===")
    
    # Get a test client and show
    try:
        client = Client.objects.first()
        show = Show.objects.first()
        user = User.objects.first()
        
        if not client or not show or not user:
            print("❌ Missing required test data (client, show, or user)")
            return
            
        print(f"Using client: {client.name} (Organization: {client.organization.name})")
        print(f"Using show: {show.name} (Start: {show.start_time}, End: {show.end_time})")
        print(f"Using user: {user.username}")

        # Calculate a valid time within the show's schedule
        show_start_hour = show.start_time.hour
        show_start_minute = show.start_time.minute
        # Use a time 30 minutes after the show starts
        valid_time = time(show_start_hour, show_start_minute + 30) if show_start_minute <= 30 else time(show_start_hour + 1, show_start_minute - 30)
        print(f"Using scheduled time: {valid_time}")
        
        # Check organization settings
        try:
            org_settings = OrganizationSettings.objects.get(organization=client.organization)
            print(f"Organization settings found:")
            print(f"  - require_mention_approval: {org_settings.require_mention_approval}")
            print(f"  - auto_approve_recurring: {org_settings.auto_approve_recurring}")
        except OrganizationSettings.DoesNotExist:
            print("❌ No organization settings found!")
            return
        
        # Create a test recurring mention
        recurring_mention = RecurringMention.objects.create(
            title='Test Auto-Approval New Recurring',
            content='This new recurring mention should be automatically approved',
            client=client,
            frequency='weekly',
            weekdays=[0, 1, 2, 3, 4],  # Monday to Friday
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            created_by=user
        )
        
        print(f"✅ Created recurring mention: {recurring_mention.title} (ID: {recurring_mention.id})")
        
        # Create show assignment (this should trigger automatic mention generation via signal)
        show_assignment = RecurringMentionShow.objects.create(
            recurring_mention=recurring_mention,
            show=show,
            scheduled_time=valid_time,
            scheduled_days=[0, 1, 2, 3, 4]
        )
        
        print(f"✅ Created show assignment: {show.name} at {show_assignment.scheduled_time}")
        
        # Check if mentions were generated and their approval status
        generated_mentions = Mention.objects.filter(recurring_mention=recurring_mention)
        
        print(f"\n=== Generated Mentions Status ===")
        print(f"Total mentions generated: {generated_mentions.count()}")
        
        if generated_mentions.count() == 0:
            print("❌ No mentions were generated!")
            return
            
        approved_count = 0
        pending_count = 0
        
        for mention in generated_mentions:
            print(f"Mention: '{mention.title[:50]}...' - Status: {mention.status}")
            if mention.status == 'scheduled':
                approved_count += 1
                print(f"  ✅ Approved by: {mention.approved_by}")
                print(f"  ✅ Approved at: {mention.approved_at}")
            elif mention.status == 'pending':
                pending_count += 1
                print(f"  ⚠️  Pending approval")
        
        print(f"\n=== Summary ===")
        print(f"✅ Approved mentions: {approved_count}")
        print(f"⚠️  Pending mentions: {pending_count}")
        
        if approved_count > 0 and pending_count == 0:
            print("🎉 SUCCESS: All new recurring mentions were automatically approved!")
        elif pending_count > 0:
            print("❌ ISSUE: Some mentions are still pending approval")
        else:
            print("❌ ISSUE: No mentions were generated")
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_recurring_mention_approval()
