# Generated by Django 4.2.7 on 2025-07-12 17:05

from django.db import migrations, models


def enable_auto_approve_recurring_for_existing_orgs(apps, schema_editor):
    """
    Enable auto_approve_recurring for all existing organizations.
    This ensures that existing organizations get the new automatic approval behavior.
    """
    OrganizationSettings = apps.get_model('settings', 'OrganizationSettings')

    # Update all existing organization settings to enable auto_approve_recurring
    updated_count = OrganizationSettings.objects.filter(
        auto_approve_recurring=False
    ).update(auto_approve_recurring=True)

    print(f"Updated {updated_count} organization settings to enable auto_approve_recurring")


def disable_auto_approve_recurring_for_existing_orgs(apps, schema_editor):
    """
    Reverse migration: disable auto_approve_recurring for all organizations.
    """
    OrganizationSettings = apps.get_model('settings', 'OrganizationSettings')

    # Revert all organization settings to disable auto_approve_recurring
    updated_count = OrganizationSettings.objects.filter(
        auto_approve_recurring=True
    ).update(auto_approve_recurring=False)

    print(f"Reverted {updated_count} organization settings to disable auto_approve_recurring")


class Migration(migrations.Migration):
    dependencies = [
        ("settings", "0007_organizationsettings_assignment_reminder_hours_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="organizationsettings",
            name="auto_approve_recurring",
            field=models.BooleanField(default=True),
        ),
        migrations.RunPython(
            enable_auto_approve_recurring_for_existing_orgs,
            disable_auto_approve_recurring_for_existing_orgs,
        ),
    ]
