from django.db import models
from django.contrib.auth.models import User
from apps.core.models import TimeStampedModel


class OrganizationSettings(TimeStampedModel):
    """Organization-level settings"""
    organization = models.OneToOneField(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='settings'
    )

    # Time and scheduling settings
    default_mention_duration = models.PositiveIntegerField(default=30, help_text="Default duration in seconds")
    max_mentions_per_hour = models.PositiveIntegerField(default=6)
    min_gap_between_mentions = models.PositiveIntegerField(default=300, help_text="Minimum gap in seconds")
    allow_overlapping_mentions = models.BooleanField(default=False)

    # Conflict detection settings
    enable_conflict_detection = models.BooleanField(default=True)
    auto_resolve_conflicts = models.BooleanField(default=False)
    conflict_resolution_strategy = models.CharField(
        max_length=20,
        choices=[
            ('priority', 'By Priority'),
            ('first_come', 'First Come First Serve'),
            ('manual', 'Manual Resolution'),
        ],
        default='priority'
    )

    # Notification settings
    enable_email_notifications = models.BooleanField(default=True)
    enable_conflict_alerts = models.BooleanField(default=True)
    enable_deadline_reminders = models.BooleanField(default=True)
    reminder_hours_before = models.PositiveIntegerField(default=24)

    # Approval workflow settings
    require_mention_approval = models.BooleanField(default=True)
    auto_approve_recurring = models.BooleanField(default=True)
    approval_timeout_hours = models.PositiveIntegerField(default=48)

    # Archive and cleanup settings
    auto_archive_completed = models.BooleanField(default=True)
    archive_after_days = models.PositiveIntegerField(default=30)
    delete_archived_after_days = models.PositiveIntegerField(default=365)

    # Live show display settings
    mention_alignment_mode = models.CharField(
        max_length=20,
        choices=[
            ('alternating', 'Alternating (First Right, Then Left)'),
            ('industry_based', 'Based on Industry Settings'),
            ('all_left', 'All Left Aligned'),
            ('all_right', 'All Right Aligned'),
        ],
        default='alternating',
        help_text='How mentions should be aligned in live show view'
    )

    # Industry alignment settings (JSON field to store industry -> alignment mapping)
    industry_alignment_settings = models.JSONField(
        default=dict,
        help_text='JSON mapping of industry codes to alignment (left/right)',
        blank=True
    )

    # News Reader Settings
    default_article_length_minutes = models.PositiveIntegerField(default=2, help_text="Default article length in minutes")
    max_articles_per_session = models.PositiveIntegerField(default=10, help_text="Maximum articles per reading session")
    auto_save_interval = models.PositiveIntegerField(default=30, help_text="Auto-save interval in seconds")

    # Reading settings
    default_reading_speed = models.PositiveIntegerField(default=3, help_text="Default reading speed (1-5 scale)")
    enable_teleprompter_mode = models.BooleanField(default=True, help_text="Enable teleprompter mode")
    teleprompter_font_size = models.PositiveIntegerField(default=32, help_text="Default teleprompter font size")

    # Assignment settings
    require_article_approval = models.BooleanField(default=False, help_text="Require approval before publishing articles")
    auto_assign_breaking_news = models.BooleanField(default=True, help_text="Auto-assign breaking news to available readers")
    assignment_reminder_hours = models.PositiveIntegerField(default=2, help_text="Hours before due date to send reminders")

    # Live reading settings
    enable_live_reading_mode = models.BooleanField(default=True, help_text="Enable live reading interface")
    live_reading_timeout_minutes = models.PositiveIntegerField(default=30, help_text="Live reading session timeout")
    enable_reading_analytics = models.BooleanField(default=True, help_text="Enable reading performance analytics")

    class Meta:
        verbose_name = 'Organization Settings'
        verbose_name_plural = 'Organization Settings'

    def __str__(self):
        return f"Settings for {self.organization.name}"


class UserPreferences(TimeStampedModel):
    """User-specific preferences"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='preferences')
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='user_preferences'
    )

    # Dashboard preferences
    default_dashboard_view = models.CharField(
        max_length=20,
        choices=[
            ('calendar', 'Calendar View'),
            ('list', 'List View'),
            ('kanban', 'Kanban Board'),
        ],
        default='calendar'
    )
    items_per_page = models.PositiveIntegerField(default=20)
    show_completed_mentions = models.BooleanField(default=False)

    # Calendar preferences
    calendar_default_view = models.CharField(
        max_length=10,
        choices=[
            ('day', 'Day'),
            ('week', 'Week'),
            ('month', 'Month'),
        ],
        default='week'
    )
    calendar_start_hour = models.PositiveIntegerField(default=6)
    calendar_end_hour = models.PositiveIntegerField(default=24)

    # Notification preferences
    email_notifications = models.BooleanField(default=True)
    browser_notifications = models.BooleanField(default=True)
    mobile_notifications = models.BooleanField(default=True)

    # Notification types
    notify_mention_assigned = models.BooleanField(default=True)
    notify_mention_approved = models.BooleanField(default=True)
    notify_mention_rejected = models.BooleanField(default=True)
    notify_conflicts = models.BooleanField(default=True)
    notify_deadlines = models.BooleanField(default=True)
    notify_schedule_changes = models.BooleanField(default=True)

    # Theme and UI preferences
    theme = models.CharField(
        max_length=10,
        choices=[
            ('light', 'Light'),
            ('dark', 'Dark'),
            ('auto', 'Auto'),
        ],
        default='light'
    )
    sidebar_collapsed = models.BooleanField(default=False)

    # Time and localization preferences
    timezone = models.CharField(
        max_length=50,
        choices=[
            ('UTC', 'UTC'),
            ('Europe/Moscow', 'Moscow Time (UTC+3)'),
            ('Europe/Istanbul', 'Turkey Time (UTC+3)'),
            ('Africa/Nairobi', 'East Africa Time (UTC+3)'),
            ('Asia/Riyadh', 'Arabia Standard Time (UTC+3)'),
            ('Europe/Athens', 'Eastern European Time (UTC+2/+3)'),
            ('Europe/London', 'Greenwich Mean Time (UTC+0/+1)'),
            ('Europe/Paris', 'Central European Time (UTC+1/+2)'),
            ('America/New_York', 'Eastern Time (UTC-5/-4)'),
            ('America/Chicago', 'Central Time (UTC-6/-5)'),
            ('America/Denver', 'Mountain Time (UTC-7/-6)'),
            ('America/Los_Angeles', 'Pacific Time (UTC-8/-7)'),
            ('America/Anchorage', 'Alaska Time (UTC-9/-8)'),
            ('Pacific/Honolulu', 'Hawaii Time (UTC-10)'),
            ('Asia/Tokyo', 'Japan Standard Time (UTC+9)'),
            ('Australia/Sydney', 'Australian Eastern Time (UTC+10/+11)'),
        ],
        default='Africa/Nairobi',
        help_text='Your preferred timezone for displaying dates and times'
    )

    date_format = models.CharField(
        max_length=20,
        choices=[
            ('MM/DD/YYYY', 'MM/DD/YYYY (US Format)'),
            ('DD/MM/YYYY', 'DD/MM/YYYY (European Format)'),
            ('YYYY-MM-DD', 'YYYY-MM-DD (ISO Format)'),
            ('MMM DD, YYYY', 'MMM DD, YYYY (e.g., Jan 15, 2024)'),
            ('DD MMM YYYY', 'DD MMM YYYY (e.g., 15 Jan 2024)'),
        ],
        default='MM/DD/YYYY',
        help_text='How dates should be displayed throughout the application'
    )

    time_format = models.CharField(
        max_length=10,
        choices=[
            ('12', '12-hour (AM/PM)'),
            ('24', '24-hour (Military Time)'),
        ],
        default='12',
        help_text='Whether to display time in 12-hour or 24-hour format'
    )

    language = models.CharField(
        max_length=10,
        choices=[
            ('en', 'English'),
            ('es', 'Spanish'),
            ('fr', 'French'),
            ('de', 'German'),
        ],
        default='en',
        help_text='Interface language preference'
    )

    # News Reader Preferences
    default_article_view = models.CharField(
        max_length=20,
        choices=[
            ('list', 'List View'),
            ('grid', 'Grid View'),
            ('compact', 'Compact View'),
        ],
        default='list',
        help_text='Default view for article lists'
    )
    articles_per_page = models.PositiveIntegerField(default=20, help_text="Number of articles to show per page")
    show_pronunciation_guide = models.BooleanField(default=True, help_text="Show pronunciation guide in articles")
    enable_reading_notifications = models.BooleanField(default=True, help_text="Enable notifications for reading assignments")
    preferred_reading_speed = models.PositiveIntegerField(default=3, help_text="Preferred reading speed (1-5 scale)")

    class Meta:
        unique_together = ['user', 'organization']
        verbose_name = 'User Preferences'
        verbose_name_plural = 'User Preferences'

    def __str__(self):
        return f"Preferences for {self.user.username} in {self.organization.name}"


class SystemSettings(TimeStampedModel):
    """System-wide settings"""
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'System Setting'
        verbose_name_plural = 'System Settings'

    def __str__(self):
        return f"{self.key}: {self.value[:50]}..."


class APISettings(TimeStampedModel):
    """API integration settings for organizations"""
    organization = models.OneToOneField(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='api_settings'
    )

    # OpenWeatherMap API
    openweather_api_key = models.CharField(
        max_length=255,
        blank=True,
        help_text="API key from OpenWeatherMap.org"
    )
    openweather_enabled = models.BooleanField(default=False)
    weather_location = models.CharField(
        max_length=100,
        blank=True,
        help_text="City name for weather data (e.g., 'New York, NY, US')"
    )
    weather_units = models.CharField(
        max_length=10,
        choices=[
            ('metric', 'Celsius'),
            ('imperial', 'Fahrenheit'),
            ('kelvin', 'Kelvin'),
        ],
        default='imperial'
    )

    # Future API integrations can be added here
    # spotify_api_key = models.CharField(max_length=255, blank=True)
    # twitter_api_key = models.CharField(max_length=255, blank=True)
    # etc.

    class Meta:
        verbose_name = 'API Settings'
        verbose_name_plural = 'API Settings'

    def __str__(self):
        return f"API Settings for {self.organization.name}"

    @property
    def has_weather_api(self):
        """Check if weather API is properly configured"""
        return bool(self.openweather_api_key and self.openweather_enabled and self.weather_location)
