"""
Django signals for the mentions app.
Handles automatic approval and processing of recurring mentions.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from .models import RecurringMention, RecurringMentionShow
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=RecurringMention)
def recurring_mention_post_save(sender, instance, created, **kwargs):
    """
    Handle RecurringMention post-save processing:
    - Automatically generate initial mentions when a recurring mention is created
    - Ensure proper approval status based on organization settings
    """
    if created:
        logger.info(f"New recurring mention created: {instance.title} (ID: {instance.id})")
        
        # Check if the recurring mention has show assignments
        # We need to wait for the show assignments to be created in the same transaction
        # So we'll defer the mention generation to avoid race conditions
        
        # Log the creation for monitoring
        logger.info(f"Recurring mention '{instance.title}' created successfully. "
                   f"Individual mentions will be generated when show assignments are added.")


@receiver(post_save, sender=RecurringMentionShow)
def recurring_mention_show_post_save(sender, instance, created, **kwargs):
    """
    Handle RecurringMentionShow post-save processing:
    - Generate mentions when show assignments are added to a recurring mention
    - Ensure automatic approval based on organization settings
    """
    if created:
        recurring_mention = instance.recurring_mention
        logger.info(f"Show assignment created for recurring mention '{recurring_mention.title}': "
                   f"{instance.show.name} at {instance.scheduled_time}")

        # Check if mentions have already been generated for this recurring mention
        existing_mentions_count = recurring_mention.mention_set.count()

        # Only generate mentions if none exist yet (prevents duplicate generation)
        if existing_mentions_count == 0:
            try:
                # Generate mentions for the recurring pattern
                generated_mentions = recurring_mention.generate_mentions()

                logger.info(f"Auto-generated {len(generated_mentions)} mentions for recurring mention "
                           f"'{recurring_mention.title}' after show assignment creation")

                # Log approval status
                if generated_mentions:
                    first_mention = generated_mentions[0]
                    if first_mention.status == 'scheduled':
                        logger.info(f"Generated mentions for '{recurring_mention.title}' were automatically approved")
                    else:
                        logger.info(f"Generated mentions for '{recurring_mention.title}' require manual approval")

            except Exception as e:
                logger.error(f"Error generating mentions for recurring mention '{recurring_mention.title}': {str(e)}")
        else:
            logger.info(f"Mentions already exist for recurring mention '{recurring_mention.title}' ({existing_mentions_count} mentions), skipping generation")
