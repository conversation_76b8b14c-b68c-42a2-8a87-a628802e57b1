from django.db import models
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from apps.core.models import TimeStampedModel, Client, Presenter
from apps.shows.models import Show
from datetime import timedelta
from datetime import datetime, timedelta
# dateutil.rrule imports removed - using simple date iteration instead
import logging
from django.db.models import Prefetch

logger = logging.getLogger(__name__)


class Mention(TimeStampedModel):
    """Model for radio mentions"""
    STATUS_CHOICES = [
        ('pending', 'Pending Approval'),
        ('scheduled', 'Approved'),
        ('read', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]

    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    content = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)
    duration_seconds = models.PositiveIntegerField(default=30, help_text="Expected duration in seconds")
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_mentions')
    approved_at = models.DateTimeField(null=True, blank=True)

    # Link to recurring mention if this was generated from a pattern
    recurring_mention = models.ForeignKey('RecurringMention', on_delete=models.SET_NULL, null=True, blank=True,
                                        help_text="Source recurring mention if this was auto-generated")

    class Meta:
        ordering = ['-priority', '-created_at']
        indexes = [
            models.Index(fields=['status'], name='mention_status_idx'),
            models.Index(fields=['client'], name='mention_client_idx'),
            models.Index(fields=['priority'], name='mention_priority_idx'),
            models.Index(fields=['created_at'], name='mention_created_at_idx'),
            models.Index(fields=['status', 'priority'], name='mention_status_priority_idx'),
            models.Index(fields=['client', 'status'], name='mention_client_status_idx'),
            models.Index(fields=['recurring_mention'], name='mention_recurring_idx'),
            models.Index(fields=['created_by'], name='mention_created_by_idx'),
            models.Index(fields=['approved_by'], name='mention_approved_by_idx'),
        ]

    def __str__(self):
        return f"{self.title} - {self.client.name}"

    def get_absolute_url(self):
        return reverse('mentions:mention_detail', kwargs={'pk': self.pk})

    def clean(self):
        """Validate the mention before saving"""
        from django.core.exceptions import ValidationError

        # Validate duration
        if self.duration_seconds < 5:
            raise ValidationError("Duration must be at least 5 seconds")
        if self.duration_seconds > 300:
            raise ValidationError("Duration cannot exceed 300 seconds (5 minutes)")

        # Validate content length
        if len(self.content.strip()) < 10:
            raise ValidationError("Content must be at least 10 characters long")

    def can_be_scheduled(self):
        """Check if this mention can be scheduled"""
        return self.status in ['pending', 'scheduled'] and not self.mentionreading_set.exists()

    def is_scheduled(self):
        """Check if this mention has been scheduled to specific time slots"""
        return self.mentionreading_set.exists()

    @property
    def priority_display(self):
        return dict(self.PRIORITY_CHOICES)[self.priority]

    @property
    def status_display(self):
        return dict(self.STATUS_CHOICES)[self.status]

    @property
    def is_scheduled(self):
        return self.status == 'scheduled'

    @property
    def is_pending(self):
        return self.status == 'pending'

    @property
    def total_readings(self):
        return self.mentionreading_set.count()

    @property
    def completed_readings(self):
        return self.mentionreading_set.filter(actual_read_time__isnull=False).count()

    def save(self, *args, **kwargs):
        """Override save to include validation and apply approval workflow"""
        # Apply approval workflow settings if this is a new mention
        if not self.pk:
            try:
                from apps.settings.models import OrganizationSettings
                org_settings = OrganizationSettings.objects.get(organization=self.client.organization)

                # If approval is not required, automatically approve
                if not org_settings.require_mention_approval:
                    self.status = 'scheduled'
                    self.approved_by = self.created_by
                    self.approved_at = timezone.now()
                    logger.info(f"Auto-approved mention '{self.title}' - approval not required")
                # If this is a recurring mention and auto-approve is enabled
                elif self.recurring_mention and org_settings.auto_approve_recurring:
                    self.status = 'scheduled'
                    self.approved_by = self.created_by
                    self.approved_at = timezone.now()
                    logger.info(f"Auto-approved recurring mention '{self.title}' - auto-approve recurring enabled")
                # Otherwise, keep the default 'pending' status for approval
                else:
                    logger.debug(f"Mention '{self.title}' requires approval - status: {self.status}")
            except OrganizationSettings.DoesNotExist:
                # Keep default behavior if no settings exist (require approval)
                logger.debug(f"No organization settings found for {self.client.organization} - using default approval workflow")
            except Exception as e:
                logger.error(f"Error applying approval workflow for mention '{self.title}': {e}")

        self.clean()
        super().save(*args, **kwargs)


class MentionReading(TimeStampedModel):
    """Model for scheduled mention readings"""
    mention = models.ForeignKey(Mention, on_delete=models.CASCADE)
    show = models.ForeignKey(Show, on_delete=models.CASCADE)
    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, null=True, blank=True,
                                help_text="Presenter who actually read the mention (assigned when marked as read)")
    scheduled_date = models.DateField()
    scheduled_time = models.TimeField()
    actual_read_time = models.DateTimeField(null=True, blank=True)
    duration_seconds = models.IntegerField(null=True, blank=True)
    notes = models.TextField(blank=True)
    read_by = models.CharField(max_length=100, blank=True, help_text="Actual presenter name if different")

    class Meta:
        ordering = ['scheduled_date', 'scheduled_time']
        unique_together = ['mention', 'show', 'scheduled_date', 'scheduled_time']
        indexes = [
            models.Index(fields=['scheduled_date'], name='reading_scheduled_date_idx'),
            models.Index(fields=['scheduled_time'], name='reading_scheduled_time_idx'),
            models.Index(fields=['mention'], name='reading_mention_idx'),
            models.Index(fields=['show'], name='reading_show_idx'),
            models.Index(fields=['presenter'], name='reading_presenter_idx'),
            models.Index(fields=['actual_read_time'], name='reading_actual_read_time_idx'),
            models.Index(fields=['scheduled_date', 'scheduled_time'], name='reading_schedule_idx'),
            models.Index(fields=['show', 'scheduled_date'], name='reading_show_date_idx'),
            models.Index(fields=['mention', 'actual_read_time'], name='reading_mention_completed_idx'),
            # Optimized indexes for conflict checking queries
            models.Index(fields=['show', 'scheduled_date', 'actual_read_time'], name='reading_conflict_check_idx'),
            models.Index(fields=['show', 'scheduled_time', 'actual_read_time'], name='reading_time_conflict_idx'),
        ]

    def __str__(self):
        return f"{self.mention.title} - {self.show.name} on {self.scheduled_date}"

    def get_absolute_url(self):
        return reverse('mentions:reading_detail', kwargs={'pk': self.pk})

    @property
    def is_completed(self):
        return self.actual_read_time is not None

    @property
    def scheduled_datetime(self):
        from datetime import datetime
        return datetime.combine(self.scheduled_date, self.scheduled_time)

    def has_conflicts(self):
        """Check if this reading has scheduling conflicts"""
        from datetime import timedelta

        # Check if overlapping mentions are allowed in organization settings
        try:
            organization = self.mention.client.organization
            settings = organization.settings
            allow_overlapping = settings.allow_overlapping_mentions
        except:
            # If we can't get settings, default to not allowing overlaps
            allow_overlapping = False

        # Only check for time overlaps if overlapping mentions are not allowed
        if not allow_overlapping:
            # Get mentions scheduled at the same time
            overlapping = MentionReading.objects.filter(
                show=self.show,
                scheduled_date=self.scheduled_date,
                scheduled_time=self.scheduled_time
            ).exclude(pk=self.pk)

            if overlapping.exists():
                return True

            # Check for time overlap based on duration
            start_time = self.scheduled_datetime
            end_time = start_time + timedelta(seconds=self.mention.duration_seconds)

            # Find readings that might overlap
            same_day_readings = MentionReading.objects.filter(
                show=self.show,
                scheduled_date=self.scheduled_date
            ).exclude(pk=self.pk)

            for reading in same_day_readings:
                other_start = reading.scheduled_datetime
                other_end = other_start + timedelta(seconds=reading.mention.duration_seconds)

                # Check for overlap
                if (start_time < other_end and end_time > other_start):
                    return True

        # Check if show has active presenters
        if not self.show.showpresenter_set.filter(is_active=True).exists():
            return True

        # Check if show airs on this day
        if not self.show.is_day_valid_for_show(self.scheduled_date):
            return True

        return False

    @property
    def has_conflict(self):
        """Property version of has_conflicts for template access"""
        return self.has_conflicts()

    def clean(self):
        """Validate the mention reading"""
        from django.core.exceptions import ValidationError

        errors = {}

        # Validate that the scheduled time is within the show's time frame
        if self.show and self.scheduled_date and self.scheduled_time:
            validation_errors = self.show.validate_mention_time(self.scheduled_date, self.scheduled_time)
            if validation_errors:
                errors['scheduled_time'] = validation_errors

        if errors:
            raise ValidationError(errors)

    def save(self, *args, **kwargs):
        """Override save to include validation"""
        self.clean()
        super().save(*args, **kwargs)


class RecurringMention(TimeStampedModel):
    """Model for recurring mention patterns"""

    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('custom', 'Custom'),
    ]

    WEEKDAY_CHOICES = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('ended', 'Ended'),
        ('finished', 'Finished'),
        ('canceled', 'Canceled'),
        ('replaced', 'Replaced'),
        ('split', 'Split'),
    ]

    # Base mention template
    title = models.CharField(max_length=200)
    content = models.TextField()
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    priority = models.IntegerField(choices=Mention.PRIORITY_CHOICES, default=2)
    duration_seconds = models.PositiveIntegerField(default=30)

    # Enhanced recurrence settings (following iCalendar RFC 5545)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)
    interval = models.PositiveIntegerField(default=1, help_text="Every X days/weeks/months")
    weekdays = models.JSONField(default=list, help_text="List of weekday numbers for weekly recurrence (0=Monday, 6=Sunday)")
    monthdays = models.JSONField(default=list, help_text="List of month days for monthly recurrence (1-31)")

    # Campaign tracking fields
    campaign_name = models.CharField(max_length=200, blank=True, help_text="Campaign name for tracking")
    daily_frequency = models.PositiveIntegerField(default=1, help_text="Target mentions per day")
    total_required = models.PositiveIntegerField(null=True, blank=True, help_text="Total mentions required for campaign")
    total_aired = models.PositiveIntegerField(default=0, help_text="Total mentions actually aired")

    # Scheduling settings
    shows = models.ManyToManyField(Show, through='RecurringMentionShow')

    # Date range
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    max_occurrences = models.PositiveIntegerField(null=True, blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    status_changed_at = models.DateTimeField(null=True, blank=True)
    status_changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='status_changed_recurring_mentions')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['client'], name='recurring_client_idx'),
            models.Index(fields=['is_active'], name='recurring_is_active_idx'),
            models.Index(fields=['start_date'], name='recurring_start_date_idx'),
            models.Index(fields=['end_date'], name='recurring_end_date_idx'),
            models.Index(fields=['frequency'], name='recurring_frequency_idx'),
            models.Index(fields=['created_by'], name='recurring_created_by_idx'),
            models.Index(fields=['client', 'is_active'], name='recurring_client_active_idx'),
            models.Index(fields=['start_date', 'end_date'], name='recurring_date_range_idx'),
        ]

    def __str__(self):
        return f"Recurring: {self.title} ({self.get_frequency_display()})"

    def update_status(self, new_status, user=None):
        """Update the status of this recurring mention with audit trail"""
        if self.status != new_status:
            self.status = new_status
            self.status_changed_at = timezone.now()
            self.status_changed_by = user
            self.save(update_fields=['status', 'status_changed_at', 'status_changed_by'])

    def get_status_display_with_icon(self):
        """Get status display with appropriate icon"""
        status_icons = {
            'active': 'fa-play-circle text-green-500',
            'paused': 'fa-pause-circle text-orange-500',
            'ended': 'fa-stop-circle text-gray-500',
            'finished': 'fa-check-circle text-blue-500',
            'canceled': 'fa-times-circle text-red-500',
            'replaced': 'fa-exchange-alt text-orange-500',
            'split': 'fa-code-branch text-purple-500',
        }
        icon = status_icons.get(self.status, 'fa-question-circle text-gray-400')
        return f'<i class="fas {icon} mr-1"></i>{self.get_status_display()}'

    @property
    def generated_mentions_count(self):
        """Count of mentions generated from this recurring pattern"""
        return self.mention_set.count()

    @property
    def active_mentions_count(self):
        """Count of active (unread) mentions generated from this recurring pattern"""
        return self.mention_set.filter(
            status__in=['pending', 'scheduled'],
            mentionreading__actual_read_time__isnull=True
        ).count()

    @property
    def pending_mentions_count(self):
        """Count of pending mentions generated from this recurring pattern"""
        return self.mention_set.filter(status='pending').count()

    @property
    def approved_mentions_count(self):
        """Count of approved mentions generated from this recurring pattern"""
        return self.mention_set.filter(status='scheduled').count()

    @property
    def scheduled_mentions_count(self):
        """Alias for approved_mentions_count for backward compatibility"""
        return self.approved_mentions_count

    def get_related_mentions(self):
        """Get mentions that are related to this one through add/replace actions"""
        related_mentions = []

        # Find mentions that were created from this one (this is the base)
        created_from_this = MentionAuditLog.objects.filter(
            change_type='created',
            metadata__base_mention_id=self.id
        ).select_related('recurring_mention')

        for log in created_from_this:
            if log.recurring_mention and log.recurring_mention.id != self.id:
                related_mentions.append({
                    'mention': log.recurring_mention,
                    'relationship': 'additional',
                    'created_at': log.created_at,
                    'created_by': log.changed_by
                })

        # Find mentions that were replaced by this one (this is the replacement)
        replaced_mentions = MentionAuditLog.objects.filter(
            change_type='created',
            recurring_mention=self,
            metadata__replaced_mention_id__isnull=False
        ).first()

        if replaced_mentions:
            try:
                original_mention = RecurringMention.objects.get(
                    id=replaced_mentions.metadata.get('replaced_mention_id')
                )
                related_mentions.append({
                    'mention': original_mention,
                    'relationship': 'replaced',
                    'created_at': replaced_mentions.created_at,
                    'created_by': replaced_mentions.changed_by
                })
            except RecurringMention.DoesNotExist:
                pass

        # Find the base mention if this was created as additional
        base_mention_log = MentionAuditLog.objects.filter(
            change_type='created',
            recurring_mention=self,
            metadata__base_mention_id__isnull=False
        ).first()

        if base_mention_log:
            try:
                base_mention = RecurringMention.objects.get(
                    id=base_mention_log.metadata.get('base_mention_id')
                )
                related_mentions.append({
                    'mention': base_mention,
                    'relationship': 'base',
                    'created_at': base_mention_log.created_at,
                    'created_by': base_mention_log.changed_by
                })
            except RecurringMention.DoesNotExist:
                pass

        return related_mentions

    def get_relationship_type(self):
        """Get the type of relationship this mention has with others"""
        # Check if this was created as additional mention
        additional_log = MentionAuditLog.objects.filter(
            change_type='created',
            recurring_mention=self,
            metadata__additional_mention_action=True
        ).first()

        if additional_log:
            return 'additional'

        # Check if this was created as replacement
        replacement_log = MentionAuditLog.objects.filter(
            change_type='created',
            recurring_mention=self,
            metadata__replacement_action=True
        ).first()

        if replacement_log:
            return 'replacement'

        # Check if this has additional mentions created from it
        has_additional = MentionAuditLog.objects.filter(
            change_type='created',
            metadata__base_mention_id=self.id
        ).exists()

        if has_additional:
            return 'base'

        return 'standalone'

    def has_same_schedule_pattern(self, other_mention):
        """Check if this mention has the same schedule pattern as another"""
        if not isinstance(other_mention, RecurringMention):
            return False

        # Compare basic schedule settings
        if (self.frequency != other_mention.frequency or
            self.interval != other_mention.interval or
            self.weekdays != other_mention.weekdays or
            self.monthdays != other_mention.monthdays):
            return False

        # Compare show assignments
        self_shows = set()
        other_shows = set()

        for assignment in self.recurringmentionshow_set.all():
            self_shows.add((assignment.show.id, assignment.scheduled_time, tuple(sorted(assignment.scheduled_days))))

        for assignment in other_mention.recurringmentionshow_set.all():
            other_shows.add((assignment.show.id, assignment.scheduled_time, tuple(sorted(assignment.scheduled_days))))

        return self_shows == other_shows

    def generate_mentions(self, end_date=None):
        """Generate individual mentions based on recurrence pattern"""
        if not self.is_active:
            logger.debug(f"RecurringMention {self.id} is not active, skipping generation")
            return []

        # Determine end date
        if end_date is None:
            end_date = self.end_date or (self.start_date + timedelta(days=365))

        # Get organization settings for approval workflow
        try:
            from apps.settings.models import OrganizationSettings
            org_settings = OrganizationSettings.objects.get(organization=self.client.organization)
            require_approval = org_settings.require_mention_approval
            auto_approve_recurring = org_settings.auto_approve_recurring
        except OrganizationSettings.DoesNotExist:
            # Default behavior if no settings exist
            require_approval = True
            auto_approve_recurring = False

        # Simple approach: Generate all dates from start_date to end_date
        # Then filter based on which days have scheduled slots
        dates_list = []
        current_date = self.start_date

        logger.info(f"Generating dates from {self.start_date} to {end_date}")

        # Get all show assignments to see which weekdays have slots
        recurring_shows = list(self.recurringmentionshow_set.select_related('show').all())
        scheduled_weekdays = set()
        for rs in recurring_shows:
            if hasattr(rs, 'scheduled_days') and rs.scheduled_days:
                scheduled_weekdays.update(rs.scheduled_days)

        logger.info(f"Weekdays with scheduled slots: {sorted(scheduled_weekdays)}")

        # Generate dates for weekdays that have scheduled slots
        while current_date <= end_date:
            current_weekday = current_date.weekday()

            # Only include dates that have scheduled slots
            if current_weekday in scheduled_weekdays:
                # Convert to datetime for compatibility with existing code
                date_datetime = datetime.combine(current_date, datetime.min.time())
                dates_list.append(date_datetime)

            current_date += timedelta(days=1)

            # Safety check to prevent infinite loops
            if len(dates_list) > 10000:  # Reasonable limit
                logger.warning(f"Generated {len(dates_list)} dates, stopping to prevent memory issues")
                break

        logger.info(f"Generated {len(dates_list)} dates for RecurringMention {self.id} (from {self.start_date} to {end_date})")

        generated_mentions = []
        occurrence_count = 0
        skipped_count = 0
        skip_reasons = []

        # Track combinations we're creating in this generation to prevent duplicates
        created_combinations = set()

        # Get show assignments (already fetched above for date generation)
        # recurring_shows = list(self.recurringmentionshow_set.select_related('show').all())
        mention_readings = MentionReading.objects.filter(
            scheduled_date__in=[date.date() for date in dates_list]
        ).select_related('show')

        existing_mentions = Mention.objects.filter(
            recurring_mention=self
        ).prefetch_related(
            Prefetch('mentionreading_set', queryset=mention_readings)
        )

        for date in dates_list:
            if self.max_occurrences and occurrence_count >= self.max_occurrences:
                break

            for recurring_show in recurring_shows:
                if self.max_occurrences and occurrence_count >= self.max_occurrences:
                    break

                current_weekday = date.weekday()

                # Check if this show/time combination is scheduled for this specific weekday
                # This prevents duplication when the same show/time is used on multiple days
                if hasattr(recurring_show, 'scheduled_days') and recurring_show.scheduled_days:
                    if current_weekday not in recurring_show.scheduled_days:
                        skip_reason = f"Show '{recurring_show.show.name}' at {recurring_show.scheduled_time} not scheduled for {date.strftime('%A')} (weekday {current_weekday}). Scheduled for days: {recurring_show.scheduled_days}"
                        skip_reasons.append(skip_reason)
                        logger.debug(skip_reason)
                        skipped_count += 1
                        continue
                    else:
                        logger.debug(f"✅ Show '{recurring_show.show.name}' at {recurring_show.scheduled_time} IS scheduled for {date.strftime('%A')} (weekday {current_weekday})")

                        # Additional debug logging to catch duplicates
                        logger.debug(f"Processing: Date={date.date()}, Show={recurring_show.show.name}, Time={recurring_show.scheduled_time}, Weekday={current_weekday}")

                # Also check if the show itself airs on this day (legacy check)
                show_airs_on_day = True
                if hasattr(recurring_show.show, 'days_of_week') and recurring_show.show.days_of_week:
                    if isinstance(recurring_show.show.days_of_week, list) and len(recurring_show.show.days_of_week) > 0:
                        show_airs_on_day = current_weekday in recurring_show.show.days_of_week
                        if not show_airs_on_day:
                            skip_reason = f"Show '{recurring_show.show.name}' doesn't air on {date.strftime('%A')} (weekday {current_weekday}). Show airs on: {recurring_show.show.days_of_week}"
                            skip_reasons.append(skip_reason)
                            logger.debug(skip_reason)
                            skipped_count += 1
                            continue
                    else:
                        logger.debug(f"Show '{recurring_show.show.name}' has no days_of_week configured, allowing all days")
                else:
                    logger.debug(f"Show '{recurring_show.show.name}' has no days_of_week attribute, allowing all days")

                # Create unique combination key to prevent duplicates
                # Include recurring_show.id to handle cases where multiple RecurringMentionShow records exist
                combination_key = (date.date(), recurring_show.show.id, recurring_show.scheduled_time, recurring_show.id)

                # Check if mention already exists in database
                existing_check = any(
                    reading.scheduled_date == date.date() and
                    reading.show == recurring_show.show and
                    reading.scheduled_time == recurring_show.scheduled_time
                    for mention in existing_mentions
                    for reading in mention.mentionreading_set.all()
                )

                # Check if we're already creating this combination in this generation
                if combination_key in created_combinations or existing_check:
                    skip_reason = f"Mention already exists or being created for {date.date()} at {recurring_show.scheduled_time} on show '{recurring_show.show.name}' (RecurringMentionShow ID: {recurring_show.id})"
                    skip_reasons.append(skip_reason)
                    logger.debug(skip_reason)
                    skipped_count += 1
                    continue

                # Mark this combination as being created
                created_combinations.add(combination_key)
                logger.debug(f"Creating mention: {combination_key}")

                try:
                    # Determine initial status based on organization settings
                    if not require_approval:
                        # If approval is not required, mentions are automatically scheduled
                        initial_status = 'scheduled'
                        approved_by = self.created_by
                        approved_at = timezone.now()
                    elif auto_approve_recurring:
                        # If auto-approve recurring is enabled, recurring mentions are automatically approved
                        initial_status = 'scheduled'
                        approved_by = self.created_by
                        approved_at = timezone.now()
                    else:
                        # Default: require approval
                        initial_status = 'pending'
                        approved_by = None
                        approved_at = None

                    mention = Mention(
                        title=self.title,
                        content=self.content,
                        client=self.client,
                        priority=self.priority,
                        duration_seconds=self.duration_seconds,
                        status=initial_status,
                        created_by=self.created_by,
                        approved_by=approved_by,
                        approved_at=approved_at,
                        recurring_mention=self
                    )

                    # Temporarily store mention and reading to separate lists
                    mention._recurring_reading = MentionReading(
                        mention=mention,
                        show=recurring_show.show,
                        presenter=None,
                        scheduled_date=date.date(),
                        scheduled_time=recurring_show.scheduled_time
                    )

                    generated_mentions.append(mention)
                    occurrence_count += 1
                    logger.debug(f"Prepared mention for {date.date()} at {recurring_show.scheduled_time} on show '{recurring_show.show.name}'")

                except Exception as e:
                    skip_reason = f"Error preparing mention for {date.date()} at {recurring_show.scheduled_time} on show '{recurring_show.show.name}': {str(e)}"
                    skip_reasons.append(skip_reason)
                    logger.error(skip_reason)
                    skipped_count += 1

        # Save mentions individually
        # Note: Auto-approval logic was already applied above when creating mention objects
        for mention in generated_mentions:
            # Save without triggering auto-approval logic again (status already set correctly)
            super(Mention, mention).save()

            # Save the associated reading
            mention._recurring_reading.mention = mention
            mention._recurring_reading.save()

        logger.info(f"RecurringMention {self.id} generation complete: {len(generated_mentions)} created, {skipped_count} skipped")
        logger.info(f"Generation summary:")
        logger.info(f"  Date range: {self.start_date} to {self.end_date}")
        logger.info(f"  Weekdays configured: {self.weekdays}")
        logger.info(f"  Show assignments: {len(recurring_shows)}")
        for rs in recurring_shows:
            logger.info(f"    - {rs.show.name} at {rs.scheduled_time} for days {rs.scheduled_days}")
        logger.info(f"  Total dates processed: {occurrence_count + skipped_count}")
        logger.info(f"  Mentions created: {len(generated_mentions)}")
        logger.info(f"  Mentions skipped: {skipped_count}")

        if skip_reasons:
            logger.info(f"Skip reasons (first 10): {skip_reasons[:10]}")

        return generated_mentions


class RecurringMentionShow(TimeStampedModel):
    """Through model for RecurringMention and Show relationship"""
    recurring_mention = models.ForeignKey(RecurringMention, on_delete=models.CASCADE)
    show = models.ForeignKey(Show, on_delete=models.CASCADE)
    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, null=True, blank=True,
                                help_text="Presenter will be assigned when mention is read during show")
    scheduled_time = models.TimeField()
    scheduled_days = models.JSONField(default=list, help_text="List of weekday numbers this show/time is scheduled for (0=Monday, 6=Sunday)")
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['recurring_mention', 'show', 'scheduled_time']
        indexes = [
            models.Index(fields=['recurring_mention'], name='recurring_show_mention_idx'),
            models.Index(fields=['show'], name='recurring_show_show_idx'),
            models.Index(fields=['presenter'], name='recurring_show_presenter_idx'),
            models.Index(fields=['scheduled_time'], name='recurring_show_time_idx'),
            models.Index(fields=['is_active'], name='recurring_show_active_idx'),
            models.Index(fields=['show', 'is_active'], name='recurring_show_show_active_idx'),
        ]

    def __str__(self):
        return f"{self.recurring_mention.title} - {self.show.name} at {self.scheduled_time}"

    def clean(self):
        """Validate that the scheduled time is within the show's time frame"""
        from django.core.exceptions import ValidationError

        if self.show and self.scheduled_time:
            # Check if the scheduled time is within the show's time frame
            if not self.show.is_time_within_show(self.scheduled_time):
                time_str = self.scheduled_time.strftime('%I:%M %p')
                raise ValidationError({
                    'scheduled_time': f"Time {time_str} is outside show '{self.show.name}' time frame ({self.show.get_time_frame_display()})"
                })

    def save(self, *args, **kwargs):
        """Override save to include validation"""
        self.clean()
        super().save(*args, **kwargs)


class MentionAuditLog(TimeStampedModel):
    """Model for tracking mention changes and rescheduling history"""

    CHANGE_TYPES = [
        ('reschedule', 'Rescheduled'),
        ('content_update', 'Content Updated'),
        ('status_change', 'Status Changed'),
        ('schedule_change', 'Schedule Changed'),
        ('created', 'Created'),
        ('deleted', 'Deleted'),
    ]

    # What was changed
    mention = models.ForeignKey(Mention, on_delete=models.CASCADE, null=True, blank=True,
                               help_text="Regular mention that was changed")
    recurring_mention = models.ForeignKey(RecurringMention, on_delete=models.CASCADE, null=True, blank=True,
                                        help_text="Recurring mention that was changed")

    # Change details
    change_type = models.CharField(max_length=20, choices=CHANGE_TYPES)
    description = models.TextField(help_text="Human-readable description of the change")

    # Original data (before change)
    original_data = models.JSONField(default=dict, help_text="Original mention data before change")

    # New data (after change)
    new_data = models.JSONField(default=dict, help_text="New mention data after change")

    # Who made the change
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Additional metadata
    metadata = models.JSONField(default=dict, blank=True, help_text="Additional context about the change")

    # Request information for audit trail
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['mention'], name='audit_mention_idx'),
            models.Index(fields=['recurring_mention'], name='audit_recurring_mention_idx'),
            models.Index(fields=['change_type'], name='audit_change_type_idx'),
            models.Index(fields=['changed_by'], name='audit_changed_by_idx'),
            models.Index(fields=['created_at'], name='audit_created_at_idx'),
            models.Index(fields=['mention', 'change_type'], name='audit_mention_change_idx'),
            models.Index(fields=['recurring_mention', 'change_type'], name='audit_recurring_change_idx'),
        ]

    def __str__(self):
        target = self.mention or self.recurring_mention
        target_type = "Mention" if self.mention else "Recurring Mention"
        return f"{target_type} {target.id if target else 'Unknown'} - {self.get_change_type_display()}"

    @classmethod
    def log_change(cls, change_type, description, original_data=None, new_data=None,
                   mention=None, recurring_mention=None, changed_by=None,
                   metadata=None, request=None):
        """Helper method to create audit log entries"""
        log_data = {
            'change_type': change_type,
            'description': description,
            'original_data': original_data or {},
            'new_data': new_data or {},
            'mention': mention,
            'recurring_mention': recurring_mention,
            'changed_by': changed_by,
            'metadata': metadata or {},
        }

        if request:
            log_data['ip_address'] = cls._get_client_ip(request)
            log_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')

        return cls.objects.create(**log_data)

    @staticmethod
    def _get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def get_default_presenter(self):
        """Get the primary presenter for this show, or first available presenter"""
        # Try to get primary presenter first
        primary_presenter = self.show.showpresenter_set.filter(is_primary=True, is_active=True).first()
        if primary_presenter:
            return primary_presenter.presenter

        # Fall back to any active presenter for this show
        any_presenter = self.show.showpresenter_set.filter(is_active=True).first()
        if any_presenter:
            return any_presenter.presenter

        # Last resort: any active presenter in the organization
        from apps.organizations.middleware import get_current_organization
        if hasattr(self.show, 'organization'):
            return Presenter.objects.filter(organization=self.show.organization, is_active=True).first()

        return None
