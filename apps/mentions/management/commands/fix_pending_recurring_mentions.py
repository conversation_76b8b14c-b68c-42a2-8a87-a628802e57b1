"""
Management command to fix pending recurring mentions that should be auto-approved.
This command applies the auto-approval logic to existing pending recurring mentions
that were created before the auto-approval functionality was implemented.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.mentions.models import Mention
from apps.settings.models import OrganizationSettings
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fix pending recurring mentions that should be auto-approved based on organization settings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without actually making changes',
        )
        parser.add_argument(
            '--organization',
            type=str,
            help='Only process mentions for a specific organization (by name)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        org_filter = options.get('organization')
        
        self.stdout.write('=== Fixing Pending Recurring Mentions ===')
        
        # Find all pending recurring mentions
        pending_mentions = Mention.objects.filter(
            status='pending',
            recurring_mention__isnull=False
        ).select_related('client__organization', 'recurring_mention', 'created_by')
        
        if org_filter:
            pending_mentions = pending_mentions.filter(
                client__organization__name__icontains=org_filter
            )
        
        total_mentions = pending_mentions.count()
        self.stdout.write(f'Found {total_mentions} pending recurring mentions')
        
        if total_mentions == 0:
            self.stdout.write(self.style.SUCCESS('No pending recurring mentions found.'))
            return
        
        approved_count = 0
        skipped_count = 0
        error_count = 0
        
        for mention in pending_mentions:
            try:
                org = mention.client.organization
                
                # Get organization settings
                try:
                    org_settings = OrganizationSettings.objects.get(organization=org)
                except OrganizationSettings.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(
                            f'Skipping mention "{mention.title[:50]}..." - '
                            f'No organization settings for {org.name}'
                        )
                    )
                    skipped_count += 1
                    continue
                
                # Check if this mention should be auto-approved
                should_approve = False
                reason = ""
                
                if not org_settings.require_mention_approval:
                    should_approve = True
                    reason = "approval not required"
                elif org_settings.auto_approve_recurring:
                    should_approve = True
                    reason = "auto-approve recurring enabled"
                
                if should_approve:
                    if dry_run:
                        self.stdout.write(
                            f'[DRY RUN] Would approve: "{mention.title[:50]}..." '
                            f'({org.name}) - {reason}'
                        )
                    else:
                        # Apply approval
                        mention.status = 'scheduled'
                        mention.approved_by = mention.created_by
                        mention.approved_at = timezone.now()
                        mention.save()
                        
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✅ Approved: "{mention.title[:50]}..." '
                                f'({org.name}) - {reason}'
                            )
                        )
                    
                    approved_count += 1
                else:
                    self.stdout.write(
                        f'Skipping: "{mention.title[:50]}..." '
                        f'({org.name}) - requires manual approval'
                    )
                    skipped_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error processing mention "{mention.title[:50]}...": {str(e)}'
                    )
                )
                error_count += 1
        
        # Summary
        self.stdout.write('\n=== Summary ===')
        if dry_run:
            self.stdout.write(f'Would approve: {approved_count} mentions')
        else:
            self.stdout.write(self.style.SUCCESS(f'Approved: {approved_count} mentions'))
        
        self.stdout.write(f'Skipped: {skipped_count} mentions')
        
        if error_count > 0:
            self.stdout.write(self.style.ERROR(f'Errors: {error_count} mentions'))
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    '\nThis was a dry run. Use --no-dry-run to actually apply changes.'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n✅ Successfully processed {total_mentions} mentions!'
                )
            )
