"""
Test cases for automatic approval functionality of recurring mentions.
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, time, timedelta

from apps.organizations.models import Organization, OrganizationMembership
from apps.settings.models import OrganizationSettings
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.mentions.models import RecurringMention, RecurringMentionShow, Mention


class AutoApprovalTestCase(TestCase):
    """Test automatic approval functionality for recurring mentions"""
    
    def setUp(self):
        """Set up test data"""
        # Create organization
        self.organization = Organization.objects.create(
            name='Test Radio Station',
            slug='test-radio-station'
        )
        
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create organization membership
        OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='admin',
            is_default=True
        )
        
        # Create organization settings with auto_approve_recurring enabled
        self.org_settings = OrganizationSettings.objects.create(
            organization=self.organization,
            require_mention_approval=True,
            auto_approve_recurring=True  # This should be the new default
        )
        
        # Create client
        self.client = Client.objects.create(
            name='Test Client',
            organization=self.organization,
            email='<EMAIL>'
        )
        
        # Create presenter
        self.presenter = Presenter.objects.create(
            first_name='Test',
            last_name='Presenter',
            organization=self.organization,
            email='<EMAIL>'
        )
        
        # Create show
        self.show = Show.objects.create(
            name='Morning Show',
            organization=self.organization,
            start_time=time(9, 0),
            end_time=time(12, 0),
            is_active=True
        )
    
    def test_new_organization_has_auto_approve_enabled(self):
        """Test that new organizations have auto_approve_recurring enabled by default"""
        # Create a new organization
        new_org = Organization.objects.create(
            name='New Radio Station',
            slug='new-radio-station'
        )
        
        # Create settings for the new organization
        new_settings = OrganizationSettings.objects.create(
            organization=new_org
        )
        
        # Should have auto_approve_recurring=True by default
        self.assertTrue(new_settings.auto_approve_recurring)
    
    def test_recurring_mention_auto_approval(self):
        """Test that recurring mentions are automatically approved when created"""
        # Create recurring mention
        recurring_mention = RecurringMention.objects.create(
            title='Test Auto Approval',
            content='This should be automatically approved',
            client=self.client,
            frequency='weekly',
            weekdays=[0, 1, 2, 3, 4],  # Monday to Friday
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            created_by=self.user
        )
        
        # Create show assignment
        RecurringMentionShow.objects.create(
            recurring_mention=recurring_mention,
            show=self.show,
            presenter=self.presenter,
            scheduled_time=time(10, 0),
            scheduled_days=[0, 1, 2, 3, 4]
        )
        
        # Check mentions that were automatically generated by the signal
        generated_mentions = Mention.objects.filter(recurring_mention=recurring_mention)

        # Verify that generated mentions are automatically approved
        self.assertGreater(generated_mentions.count(), 0, "Should generate at least one mention")

        for mention in generated_mentions:
            self.assertEqual(mention.status, 'scheduled',
                           f"Mention '{mention.title}' should be automatically approved")
            self.assertIsNotNone(mention.approved_by,
                               f"Mention '{mention.title}' should have approved_by set")
            self.assertIsNotNone(mention.approved_at,
                               f"Mention '{mention.title}' should have approved_at set")
    
    def test_auto_approval_disabled_requires_manual_approval(self):
        """Test that when auto_approve_recurring is disabled, mentions require manual approval"""
        # Disable auto approval
        self.org_settings.auto_approve_recurring = False
        self.org_settings.save()
        
        # Create recurring mention
        recurring_mention = RecurringMention.objects.create(
            title='Test Manual Approval',
            content='This should require manual approval',
            client=self.client,
            frequency='weekly',
            weekdays=[0, 1, 2, 3, 4],  # Monday to Friday
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            created_by=self.user
        )
        
        # Create show assignment
        RecurringMentionShow.objects.create(
            recurring_mention=recurring_mention,
            show=self.show,
            presenter=self.presenter,
            scheduled_time=time(10, 0),
            scheduled_days=[0, 1, 2, 3, 4]
        )
        
        # Check mentions that were automatically generated by the signal
        generated_mentions = Mention.objects.filter(recurring_mention=recurring_mention)

        # Verify that generated mentions require manual approval
        self.assertGreater(generated_mentions.count(), 0, "Should generate at least one mention")

        for mention in generated_mentions:
            self.assertEqual(mention.status, 'pending',
                           f"Mention '{mention.title}' should require manual approval")
            self.assertIsNone(mention.approved_by,
                            f"Mention '{mention.title}' should not have approved_by set")
            self.assertIsNone(mention.approved_at,
                            f"Mention '{mention.title}' should not have approved_at set")
    
    def test_no_approval_required_auto_approves(self):
        """Test that when require_mention_approval is False, all mentions are auto-approved"""
        # Disable approval requirement entirely
        self.org_settings.require_mention_approval = False
        self.org_settings.save()
        
        # Create recurring mention
        recurring_mention = RecurringMention.objects.create(
            title='Test No Approval Required',
            content='This should be automatically approved (no approval required)',
            client=self.client,
            frequency='weekly',
            weekdays=[0, 1, 2, 3, 4],  # Monday to Friday
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            created_by=self.user
        )
        
        # Create show assignment
        RecurringMentionShow.objects.create(
            recurring_mention=recurring_mention,
            show=self.show,
            presenter=self.presenter,
            scheduled_time=time(10, 0),
            scheduled_days=[0, 1, 2, 3, 4]
        )
        
        # Check mentions that were automatically generated by the signal
        generated_mentions = Mention.objects.filter(recurring_mention=recurring_mention)

        # Verify that generated mentions are automatically approved
        self.assertGreater(generated_mentions.count(), 0, "Should generate at least one mention")

        for mention in generated_mentions:
            self.assertEqual(mention.status, 'scheduled',
                           f"Mention '{mention.title}' should be automatically approved")
            self.assertIsNotNone(mention.approved_by,
                               f"Mention '{mention.title}' should have approved_by set")
            self.assertIsNotNone(mention.approved_at,
                               f"Mention '{mention.title}' should have approved_at set")
