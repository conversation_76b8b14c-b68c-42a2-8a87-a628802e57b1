#!/usr/bin/env python
"""
Simple script to fix existing pending recurring mentions.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')
django.setup()

from django.utils import timezone
from apps.mentions.models import Mention
from apps.settings.models import OrganizationSettings
from apps.organizations.models import Organization

def create_missing_organization_settings():
    """Create default organization settings for organizations that don't have them."""
    print("=== Creating Missing Organization Settings ===")

    # Find organizations without settings
    orgs_without_settings = Organization.objects.filter(
        settings__isnull=True,
        is_active=True
    )

    created_count = 0
    for org in orgs_without_settings:
        org_settings = OrganizationSettings.objects.create(
            organization=org,
            # Use default values from the model
            default_mention_duration=30,
            max_mentions_per_hour=6,
            min_gap_between_mentions=300,
            allow_overlapping_mentions=False,
            enable_conflict_detection=True,
            auto_resolve_conflicts=False,
            conflict_resolution_strategy='priority',
            enable_email_notifications=True,
            enable_conflict_alerts=True,
            enable_deadline_reminders=True,
            reminder_hours_before=24,
            require_mention_approval=True,
            auto_approve_recurring=True,  # Enable auto-approval for recurring mentions
            approval_timeout_hours=48,
            auto_archive_completed=True,
            archive_after_days=30,
            delete_archived_after_days=365
        )
        print(f"✅ Created settings for: {org.name}")
        created_count += 1

    if created_count == 0:
        print("✅ All organizations already have settings.")
    else:
        print(f"✅ Created settings for {created_count} organizations.")

    return created_count

def fix_existing_pending_mentions():
    """Fix existing pending recurring mentions that should be auto-approved."""
    print("=== Fixing Existing Pending Recurring Mentions ===")
    
    # Find all pending recurring mentions
    pending_mentions = Mention.objects.filter(
        status='pending',
        recurring_mention__isnull=False
    ).select_related('client__organization', 'recurring_mention', 'created_by')
    
    total_mentions = pending_mentions.count()
    print(f"Found {total_mentions} pending recurring mentions")
    
    if total_mentions == 0:
        print("✅ No pending recurring mentions found.")
        return
    
    approved_count = 0
    skipped_count = 0
    
    for mention in pending_mentions:
        try:
            org = mention.client.organization
            
            # Get organization settings
            try:
                org_settings = OrganizationSettings.objects.get(organization=org)
            except OrganizationSettings.DoesNotExist:
                print(f"⚠️  Skipping mention - No organization settings for {org.name}")
                skipped_count += 1
                continue
            
            # Check if this mention should be auto-approved
            should_approve = False
            reason = ""
            
            if not org_settings.require_mention_approval:
                should_approve = True
                reason = "approval not required"
            elif org_settings.auto_approve_recurring:
                should_approve = True
                reason = "auto-approve recurring enabled"
            
            if should_approve:
                # Apply approval
                mention.status = 'scheduled'
                mention.approved_by = mention.created_by
                mention.approved_at = timezone.now()
                mention.save()
                
                print(f"✅ Approved: \"{mention.title[:50]}...\" ({org.name}) - {reason}")
                approved_count += 1
            else:
                print(f"⚠️  Skipping: \"{mention.title[:50]}...\" ({org.name}) - requires manual approval")
                skipped_count += 1
                
        except Exception as e:
            print(f"❌ Error processing mention \"{mention.title[:50]}...\": {str(e)}")
    
    print(f"\n=== Summary ===")
    print(f"✅ Approved: {approved_count} mentions")
    print(f"⚠️  Skipped: {skipped_count} mentions")
    print(f"📊 Total processed: {total_mentions} mentions")

if __name__ == "__main__":
    # First, create missing organization settings
    create_missing_organization_settings()
    print()  # Add blank line for readability

    # Then fix existing pending mentions
    fix_existing_pending_mentions()
