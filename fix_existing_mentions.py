#!/usr/bin/env python
"""
Simple script to fix existing pending recurring mentions.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')
django.setup()

from django.utils import timezone
from apps.mentions.models import Mention
from apps.settings.models import OrganizationSettings

def fix_existing_pending_mentions():
    """Fix existing pending recurring mentions that should be auto-approved."""
    print("=== Fixing Existing Pending Recurring Mentions ===")
    
    # Find all pending recurring mentions
    pending_mentions = Mention.objects.filter(
        status='pending',
        recurring_mention__isnull=False
    ).select_related('client__organization', 'recurring_mention', 'created_by')
    
    total_mentions = pending_mentions.count()
    print(f"Found {total_mentions} pending recurring mentions")
    
    if total_mentions == 0:
        print("✅ No pending recurring mentions found.")
        return
    
    approved_count = 0
    skipped_count = 0
    
    for mention in pending_mentions:
        try:
            org = mention.client.organization
            
            # Get organization settings
            try:
                org_settings = OrganizationSettings.objects.get(organization=org)
            except OrganizationSettings.DoesNotExist:
                print(f"⚠️  Skipping mention - No organization settings for {org.name}")
                skipped_count += 1
                continue
            
            # Check if this mention should be auto-approved
            should_approve = False
            reason = ""
            
            if not org_settings.require_mention_approval:
                should_approve = True
                reason = "approval not required"
            elif org_settings.auto_approve_recurring:
                should_approve = True
                reason = "auto-approve recurring enabled"
            
            if should_approve:
                # Apply approval
                mention.status = 'scheduled'
                mention.approved_by = mention.created_by
                mention.approved_at = timezone.now()
                mention.save()
                
                print(f"✅ Approved: \"{mention.title[:50]}...\" ({org.name}) - {reason}")
                approved_count += 1
            else:
                print(f"⚠️  Skipping: \"{mention.title[:50]}...\" ({org.name}) - requires manual approval")
                skipped_count += 1
                
        except Exception as e:
            print(f"❌ Error processing mention \"{mention.title[:50]}...\": {str(e)}")
    
    print(f"\n=== Summary ===")
    print(f"✅ Approved: {approved_count} mentions")
    print(f"⚠️  Skipped: {skipped_count} mentions")
    print(f"📊 Total processed: {total_mentions} mentions")

if __name__ == "__main__":
    fix_existing_pending_mentions()
