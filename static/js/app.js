/**
 * RadioMention - Main Application JavaScript
 * Contains common functionality used across the application
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('RadioMention App Loaded');
    
    // Initialize common functionality
    initializeTooltips();
    initializeModals();
    initializeFormValidation();
    initializeNotifications();
});

/**
 * Initialize tooltips for elements with data-tooltip attribute
 */
function initializeTooltips() {
    var tooltipElements = document.querySelectorAll('[data-tooltip]');
    for (var i = 0; i < tooltipElements.length; i++) {
        tooltipElements[i].addEventListener('mouseenter', showTooltip);
        tooltipElements[i].addEventListener('mouseleave', hideTooltip);
    }
}

/**
 * Show tooltip
 */
function showTooltip(event) {
    var element = event.target;
    var tooltipText = element.getAttribute('data-tooltip');
    
    if (!tooltipText) return;

    var tooltip = document.createElement('div');
    tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg';
    tooltip.textContent = tooltipText;
    tooltip.id = 'tooltip';

    document.body.appendChild(tooltip);

    // Position tooltip
    var rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

/**
 * Hide tooltip
 */
function hideTooltip() {
    var tooltip = document.getElementById('tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

/**
 * Initialize modal functionality
 */
function initializeModals() {
    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal-backdrop')) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeModal();
        }
    });
}

/**
 * Open modal
 */
function openModal(modalId) {
    var modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close modal
 */
function closeModal() {
    var modals = document.querySelectorAll('.modal');
    for (var i = 0; i < modals.length; i++) {
        modals[i].classList.add('hidden');
    }
    document.body.style.overflow = 'auto';
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    var forms = document.querySelectorAll('form[data-validate]');
    for (var i = 0; i < forms.length; i++) {
        forms[i].addEventListener('submit', validateForm);
    }
}

/**
 * Validate form before submission
 */
function validateForm(event) {
    var form = event.target;
    var requiredFields = form.querySelectorAll('[required]');
    var isValid = true;

    // Clear previous errors
    var existingErrors = form.querySelectorAll('.field-error');
    for (var j = 0; j < existingErrors.length; j++) {
        existingErrors[j].remove();
    }

    for (var i = 0; i < requiredFields.length; i++) {
        var field = requiredFields[i];
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    }

    // Only prevent default if validation fails
    // Allow normal form submission for valid forms
    if (!isValid) {
        event.preventDefault();
        console.log('Form validation failed - preventing submission');
        return false;
    }

    console.log('Form validation passed - allowing normal submission');
    return true;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('border-red-500');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'text-red-500 text-xs mt-1 field-error';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.classList.remove('border-red-500');

    var errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Initialize notifications
 */
function initializeNotifications() {
    // Auto-hide notifications after 5 seconds - but only actual notification elements
    var notifications = document.querySelectorAll('.notification-toast, .alert-notification, .flash-message');
    for (var i = 0; i < notifications.length; i++) {
        var notification = notifications[i];
        // Only auto-hide if it's actually a notification toast, not calendar or UI components
        if (!notification.closest('.calendar-container') &&
            !notification.closest('.mention-item') &&
            !notification.closest('.calendar-day') &&
            !notification.classList.contains('persistent')) {
            (function(notif) {
                setTimeout(function() {
                    hideNotification(notif);
                }, 5000);
            })(notification);
        }
    }
}

/**
 * Show notification
 */
function showNotification(message, type) {
    type = type || 'info';
    var notification = document.createElement('div');
    notification.className = 'notification fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ' + getNotificationClasses(type);
    notification.innerHTML =
        '<div class="flex items-center justify-between">' +
            '<span>' + message + '</span>' +
            '<button onclick="hideNotification(this.parentElement.parentElement)" class="ml-2 text-lg">&times;</button>' +
        '</div>';

    document.body.appendChild(notification);

    // Auto-hide after 5 seconds
    setTimeout(function() {
        hideNotification(notification);
    }, 5000);
}

/**
 * Hide notification
 */
function hideNotification(notification) {
    if (notification) {
        notification.style.opacity = '0';
        setTimeout(function() {
            notification.remove();
        }, 300);
    }
}

/**
 * Get notification CSS classes based on type
 */
function getNotificationClasses(type) {
    switch (type) {
        case 'success':
            return 'bg-green-100 border border-green-400 text-green-700';
        case 'error':
            return 'bg-red-100 border border-red-400 text-red-700';
        case 'warning':
            return 'bg-yellow-100 border border-yellow-400 text-yellow-700';
        default:
            return 'bg-blue-100 border border-blue-400 text-blue-700';
    }
}

/**
 * Utility function to make AJAX requests - IE11 compatible
 */
function makeRequest(url, options) {
    options = options || {};

    // Use the compatible AJAX manager if available, otherwise fallback to fetch
    if (window.ajax) {
        var method = options.method || 'GET';
        if (method === 'GET') {
            return window.ajax.get(url, options);
        } else if (method === 'POST') {
            return window.ajax.post(url, options.body, options);
        } else if (method === 'PUT') {
            return window.ajax.put(url, options.body, options);
        } else if (method === 'PATCH') {
            return window.ajax.patch(url, options.body, options);
        } else if (method === 'DELETE') {
            return window.ajax.delete(url, options);
        }
    }

    // Fallback to fetch for browsers that support it
    var defaults = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    };

    var config = Object.assign({}, defaults, options);

    return fetch(url, config)
        .then(function(response) {
            if (!response.ok) {
                throw new Error('HTTP error! status: ' + response.status);
            }
            return response.json();
        })
        .catch(function(error) {
            console.error('Request failed:', error);
            showNotification('Request failed. Please try again.', 'error');
            throw error;
        });
}

/**
 * Get CSRF token from meta tag or cookie
 */
function getCsrfToken() {
    var metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag) {
        return metaTag.getAttribute('content');
    }

    // Fallback to cookie
    var cookies = document.cookie.split(';');
    for (var i = 0; i < cookies.length; i++) {
        var cookie = cookies[i].trim().split('=');
        if (cookie[0] === 'csrftoken') {
            return cookie[1];
        }
    }

    return '';
}

/**
 * Format date for display
 */
function formatDate(date, format) {
    format = format || 'short';
    var d = new Date(date);

    switch (format) {
        case 'short':
            return d.toLocaleDateString();
        case 'long':
            return d.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        case 'time':
            return d.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
        default:
            return d.toLocaleString();
    }
}

/**
 * Debounce function to limit function calls - IE11 compatible
 */
function debounce(func, wait) {
    var timeout;
    return function executedFunction() {
        var args = Array.prototype.slice.call(arguments);
        var later = function() {
            clearTimeout(timeout);
            func.apply(null, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for global use
window.RadioMention = {
    openModal,
    closeModal,
    showNotification,
    hideNotification,
    makeRequest,
    formatDate,
    debounce
};
