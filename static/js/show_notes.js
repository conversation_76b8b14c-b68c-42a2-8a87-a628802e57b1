// Show Notes JavaScript functionality

// Global variables
let currentShowId = null;
let currentShowDate = null;
let currentShowName = null;
let currentNoteId = null;

// Initialize show notes functionality
function initializeShowNotes() {
    // Add event listeners for modal close on outside click
    document.getElementById('showNotesModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeShowNotesModal();
        }
    });

    document.getElementById('showNoteFormModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeShowNoteFormModal();
        }
    });

    document.getElementById('showNoteViewModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeShowNoteViewModal();
        }
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (!document.getElementById('showNoteViewModal').classList.contains('hidden')) {
                closeShowNoteViewModal();
            } else if (!document.getElementById('showNoteFormModal').classList.contains('hidden')) {
                closeShowNoteFormModal();
            } else if (!document.getElementById('showNotesModal').classList.contains('hidden')) {
                closeShowNotesModal();
            }
        }
    });
}

// Open show notes modal
function openShowNotes(showId, showDate, showName) {
    currentShowId = showId;
    currentShowDate = showDate;
    currentShowName = showName;

    // Update modal title
    document.getElementById('showNotesModalTitle').textContent = `${showName} - Notes`;
    document.getElementById('showNotesModalSubtitle').textContent = formatDate(showDate);

    // Show modal
    document.getElementById('showNotesModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Load notes
    loadShowNotes();
}

// Close show notes modal
function closeShowNotesModal() {
    document.getElementById('showNotesModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    currentShowId = null;
    currentShowDate = null;
    currentShowName = null;
}

// Load show notes via AJAX
function loadShowNotes() {
    if (!currentShowId || !currentShowDate) return;

    // Show loading state
    document.getElementById('showNotesLoading').classList.remove('hidden');
    document.getElementById('showNotesContainer').classList.add('hidden');

    fetch(`/show-notes/${currentShowId}/${currentShowDate}/`)
        .then(response => response.json())
        .then(data => {
            if (data.notes) {
                renderShowNotes(data.notes);
            } else {
                showError('Failed to load notes');
            }
        })
        .catch(error => {
            console.error('Error loading notes:', error);
            showError('Failed to load notes');
        })
        .finally(() => {
            document.getElementById('showNotesLoading').classList.add('hidden');
            document.getElementById('showNotesContainer').classList.remove('hidden');
        });
}

// Render show notes in the list
function renderShowNotes(notes) {
    const container = document.getElementById('showNotesList');
    
    if (notes.length === 0) {
        // Show empty state
        const emptyTemplate = document.getElementById('showNotesEmptyTemplate');
        container.innerHTML = emptyTemplate.innerHTML;
        return;
    }

    // Clear container
    container.innerHTML = '';

    // Render each note
    notes.forEach(note => {
        const noteElement = createNoteElement(note);
        container.appendChild(noteElement);
    });
}

// Create note element from template
function createNoteElement(note) {
    const template = document.getElementById('showNoteItemTemplate');
    const noteElement = template.content.cloneNode(true);
    const container = noteElement.querySelector('.show-note-item');

    // Set data attributes
    container.setAttribute('data-note-id', note.id);

    // Populate content
    noteElement.querySelector('.note-title').textContent = note.title;
    noteElement.querySelector('.note-content').textContent = note.content;
    noteElement.querySelector('.note-category').textContent = note.category;
    noteElement.querySelector('.note-created-at').textContent = 'Created: ' + formatDateTime(note.created_at);
    noteElement.querySelector('.note-updated-at').textContent = 'Updated: ' + formatDateTime(note.updated_at);

    // Set priority styling
    const priorityElement = noteElement.querySelector('.note-priority');
    priorityElement.textContent = note.priority_display.label;
    priorityElement.className = `note-priority text-xs px-2 py-1 rounded-full priority-${note.priority}`;

    // Set status styling
    const statusElement = noteElement.querySelector('.note-status');
    statusElement.textContent = note.status.charAt(0).toUpperCase() + note.status.slice(1);
    statusElement.className = `note-status text-xs px-2 py-1 rounded-full status-${note.status}`;

    // Show pin indicator if pinned
    if (note.is_pinned) {
        noteElement.querySelector('.note-pin-indicator').classList.remove('hidden');
    }

    // Render tags
    const tagsContainer = noteElement.querySelector('.note-tags');
    if (note.tags && note.tags.length > 0) {
        note.tags.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'note-tag';
            tagElement.textContent = tag;
            tagsContainer.appendChild(tagElement);
        });
    }

    return noteElement;
}

// Create new show note
function createShowNote() {
    if (!currentShowId || !currentShowDate) return;

    // Show form modal
    document.getElementById('showNoteFormModalTitle').textContent = 'Create Note';
    
    // Load form via AJAX
    fetch(`/show-notes/${currentShowId}/${currentShowDate}/create/`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.form_html) {
                document.getElementById('showNoteFormContainer').innerHTML = data.form_html;
                document.getElementById('showNoteFormModal').classList.remove('hidden');
                
                // Setup form submission
                setupFormSubmission('create');
            } else {
                showError('Failed to load form');
            }
        })
        .catch(error => {
            console.error('Error loading form:', error);
            showError('Failed to load form');
        });
}

// View show note
function viewShowNote(button) {
    const noteItem = button.closest('.show-note-item');
    const noteId = noteItem.getAttribute('data-note-id');
    
    fetch(`/show-notes/${noteId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.note) {
                displayNoteInViewModal(data.note);
            } else {
                showError('Failed to load note');
            }
        })
        .catch(error => {
            console.error('Error loading note:', error);
            showError('Failed to load note');
        });
}

// Display note in view modal
function displayNoteInViewModal(note) {
    currentNoteId = note.id;
    
    // Populate modal content
    document.getElementById('showNoteViewTitle').textContent = note.title;
    document.getElementById('showNoteViewContent').textContent = note.content;
    document.getElementById('showNoteViewCategory').textContent = note.category;
    document.getElementById('showNoteViewCreated').textContent = formatDateTime(note.created_at);
    document.getElementById('showNoteViewUpdated').textContent = formatDateTime(note.updated_at);

    // Set priority styling
    const priorityElement = document.getElementById('showNoteViewPriority');
    priorityElement.textContent = note.priority_display.label;
    priorityElement.className = `text-xs px-2 py-1 rounded-full priority-${note.priority}`;

    // Set status styling
    const statusElement = document.getElementById('showNoteViewStatus');
    statusElement.textContent = note.status.charAt(0).toUpperCase() + note.status.slice(1);
    statusElement.className = `text-xs px-2 py-1 rounded-full status-${note.status}`;

    // Show pin indicator if pinned
    const pinElement = document.getElementById('showNoteViewPin');
    if (note.is_pinned) {
        pinElement.classList.remove('hidden');
    } else {
        pinElement.classList.add('hidden');
    }

    // Render tags
    const tagsContainer = document.getElementById('showNoteViewTags');
    const tagsContainerWrapper = document.getElementById('showNoteViewTagsContainer');
    
    if (note.tags && note.tags.length > 0) {
        tagsContainer.innerHTML = '';
        note.tags.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'note-tag';
            tagElement.textContent = tag;
            tagsContainer.appendChild(tagElement);
        });
        tagsContainerWrapper.classList.remove('hidden');
    } else {
        tagsContainerWrapper.classList.add('hidden');
    }

    // Show modal
    document.getElementById('showNoteViewModal').classList.remove('hidden');
}

// Close view modal
function closeShowNoteViewModal() {
    document.getElementById('showNoteViewModal').classList.add('hidden');
    currentNoteId = null;
}

// Close form modal
function closeShowNoteFormModal() {
    document.getElementById('showNoteFormModal').classList.add('hidden');
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

function formatDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
    });
}

function showError(message) {
    // Use the global notification system if available
    if (window.RadioMention && window.RadioMention.showNotification) {
        window.RadioMention.showNotification(message, 'error');
    } else if (window.notificationSystem) {
        window.notificationSystem.show(message, 'error', 5000);
    } else {
        // Fallback to creating a simple notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fa-solid fa-exclamation-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    console.error('Error:', message);
}

// Edit show note
function editShowNote(button) {
    const noteItem = button.closest('.show-note-item');
    const noteId = noteItem.getAttribute('data-note-id');

    // Show form modal
    document.getElementById('showNoteFormModalTitle').textContent = 'Edit Note';

    // Load form via AJAX
    fetch(`/show-notes/${noteId}/edit/`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.form_html) {
                document.getElementById('showNoteFormContainer').innerHTML = data.form_html;
                document.getElementById('showNoteFormModal').classList.remove('hidden');

                // Setup form submission
                setupFormSubmission('edit', noteId);
            } else {
                showError('Failed to load form');
            }
        })
        .catch(error => {
            console.error('Error loading form:', error);
            showError('Failed to load form');
        });
}

// Edit note from view modal
function editShowNoteFromView() {
    if (!currentNoteId) return;

    // Close view modal first
    closeShowNoteViewModal();

    // Show form modal
    document.getElementById('showNoteFormModalTitle').textContent = 'Edit Note';

    // Load form via AJAX
    fetch(`/show-notes/${currentNoteId}/edit/`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.form_html) {
                document.getElementById('showNoteFormContainer').innerHTML = data.form_html;
                document.getElementById('showNoteFormModal').classList.remove('hidden');

                // Setup form submission
                setupFormSubmission('edit', currentNoteId);
            } else {
                showError('Failed to load form');
            }
        })
        .catch(error => {
            console.error('Error loading form:', error);
            showError('Failed to load form');
        });
}

// Setup form submission
function setupFormSubmission(action, noteId = null) {
    const form = document.getElementById('showNoteForm');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        let url;

        if (action === 'create') {
            url = `/show-notes/${currentShowId}/${currentShowDate}/create/`;
        } else {
            url = `/show-notes/${noteId}/edit/`;
        }

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                closeShowNoteFormModal();
                loadShowNotes(); // Refresh the notes list
                showSuccess(action === 'create' ? 'Note created successfully!' : 'Note updated successfully!');

                // Trigger a page refresh if we're on a page that should show updated data
                if (window.location.pathname.includes('/presenter/dashboard') ||
                    window.location.pathname.includes('/show-notes/')) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500); // Give time for success message to show
                }
            } else {
                // Handle form errors
                if (data.errors) {
                    displayFormErrors(data.errors);
                } else {
                    showError('Failed to save note');
                }
            }
        })
        .catch(error => {
            console.error('Error saving note:', error);
            showError('Failed to save note');
        });
    });
}

// Display form errors
function displayFormErrors(errors) {
    // Clear previous errors
    document.querySelectorAll('.text-red-600').forEach(el => {
        if (el.textContent.includes('This field is required') || el.textContent.includes('error')) {
            el.remove();
        }
    });

    // Display new errors
    Object.keys(errors).forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'mt-1 text-sm text-red-600';
            errorDiv.textContent = errors[fieldName].join(', ');
            field.parentNode.appendChild(errorDiv);
        }
    });
}

// Delete show note
function deleteShowNote(button) {
    const noteItem = button.closest('.show-note-item');
    const noteId = noteItem.getAttribute('data-note-id');
    const noteTitle = noteItem.querySelector('.note-title').textContent;

    if (confirm(`Are you sure you want to delete the note "${noteTitle}"?`)) {
        fetch(`/show-notes/${noteId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadShowNotes(); // Refresh the notes list
                showSuccess('Note deleted successfully!');
            } else {
                showError('Failed to delete note');
            }
        })
        .catch(error => {
            console.error('Error deleting note:', error);
            showError('Failed to delete note');
        });
    }
}

// Toggle note pin status
function toggleShowNotePin(button) {
    const noteItem = button.closest('.show-note-item');
    const noteId = noteItem.getAttribute('data-note-id');

    fetch(`/show-notes/${noteId}/toggle-pin/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadShowNotes(); // Refresh the notes list
            showSuccess(data.is_pinned ? 'Note pinned!' : 'Note unpinned!');
        } else {
            showError('Failed to toggle pin status');
        }
    })
    .catch(error => {
        console.error('Error toggling pin:', error);
        showError('Failed to toggle pin status');
    });
}

// Toggle note status
function toggleShowNoteStatus(button) {
    const noteItem = button.closest('.show-note-item');
    const noteId = noteItem.getAttribute('data-note-id');

    fetch(`/show-notes/${noteId}/toggle-status/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadShowNotes(); // Refresh the notes list
            showSuccess(`Note status changed to ${data.status_display}!`);
        } else {
            showError('Failed to toggle status');
        }
    })
    .catch(error => {
        console.error('Error toggling status:', error);
        showError('Failed to toggle status');
    });
}

// Show success message
function showSuccess(message) {
    // Use the global notification system if available
    if (window.RadioMention && window.RadioMention.showNotification) {
        window.RadioMention.showNotification(message, 'success');
    } else if (window.notificationSystem) {
        window.notificationSystem.show(message, 'success', 3000);
    } else {
        // Fallback to creating a simple notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fa-solid fa-check-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    console.log('Success:', message);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeShowNotes);
