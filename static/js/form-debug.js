/**
 * Form Debugging and Monitoring Script
 * Helps identify form submission issues and JavaScript errors
 */

(function() {
    'use strict';
    
    // Track form submissions
    let formSubmissions = [];
    let jsErrors = [];
    
    // Monitor all form submissions
    document.addEventListener('submit', function(e) {
        const form = e.target;
        const formInfo = {
            timestamp: new Date().toISOString(),
            action: form.action || window.location.href,
            method: form.method || 'GET',
            id: form.id || 'no-id',
            className: form.className || 'no-class',
            prevented: e.defaultPrevented,
            hasAjaxHandler: hasAjaxHandler(form),
            hasValidationHandler: hasValidationHandler(form)
        };
        
        formSubmissions.push(formInfo);
        console.log('Form submission detected:', formInfo);
        
        // Check if form submission was prevented
        if (e.defaultPrevented) {
            console.warn('Form submission was prevented for form:', form);
            console.warn('Form info:', formInfo);
        }
    }, true);
    
    // Monitor JavaScript errors
    window.addEventListener('error', function(e) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno,
            stack: e.error ? e.error.stack : 'No stack trace'
        };
        
        jsErrors.push(errorInfo);
        console.error('JavaScript error detected:', errorInfo);
    });
    
    // Monitor unhandled promise rejections
    window.addEventListener('unhandledrejection', function(e) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            reason: e.reason,
            type: 'unhandled-promise-rejection'
        };
        
        jsErrors.push(errorInfo);
        console.error('Unhandled promise rejection:', errorInfo);
    });
    
    // Check if form has AJAX handlers
    function hasAjaxHandler(form) {
        // Check for common AJAX indicators
        const listeners = getEventListeners ? getEventListeners(form) : null;
        if (listeners && listeners.submit) {
            return listeners.submit.some(listener => {
                const code = listener.listener.toString();
                return code.includes('preventDefault') || 
                       code.includes('fetch') || 
                       code.includes('XMLHttpRequest') ||
                       code.includes('ajax');
            });
        }
        return false;
    }
    
    // Check if form has validation handlers
    function hasValidationHandler(form) {
        return form.hasAttribute('data-validate') || 
               form.querySelector('[required]') !== null;
    }
    
    // Add debugging functions to window for console access
    window.formDebug = {
        getSubmissions: () => formSubmissions,
        getErrors: () => jsErrors,
        getLastSubmission: () => formSubmissions[formSubmissions.length - 1],
        getLastError: () => jsErrors[jsErrors.length - 1],
        clearLogs: () => {
            formSubmissions = [];
            jsErrors = [];
            console.log('Form debug logs cleared');
        },
        checkForm: (formSelector) => {
            const form = document.querySelector(formSelector);
            if (!form) {
                console.error('Form not found:', formSelector);
                return;
            }
            
            console.log('Form analysis for:', formSelector);
            console.log('Action:', form.action || 'current page');
            console.log('Method:', form.method || 'GET');
            console.log('Has required fields:', form.querySelector('[required]') !== null);
            console.log('Has data-validate:', form.hasAttribute('data-validate'));
            console.log('Has AJAX handlers:', hasAjaxHandler(form));
            console.log('Form element:', form);
        },
        testFormSubmission: (formSelector) => {
            const form = document.querySelector(formSelector);
            if (!form) {
                console.error('Form not found:', formSelector);
                return;
            }
            
            console.log('Testing form submission for:', formSelector);
            
            // Create a test submit event
            const submitEvent = new Event('submit', {
                bubbles: true,
                cancelable: true
            });
            
            const prevented = !form.dispatchEvent(submitEvent);
            console.log('Form submission prevented:', prevented);
            
            if (prevented) {
                console.warn('Form submission was prevented. Check for:');
                console.warn('1. JavaScript validation that calls preventDefault()');
                console.warn('2. AJAX handlers that prevent normal submission');
                console.warn('3. Event listeners that stop form submission');
            }
        }
    };
    
    // Auto-run basic checks when page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Form Debug Script loaded');
        console.log('Available commands:');
        console.log('- formDebug.getSubmissions() - Get all form submissions');
        console.log('- formDebug.getErrors() - Get all JavaScript errors');
        console.log('- formDebug.checkForm("form-selector") - Analyze a specific form');
        console.log('- formDebug.testFormSubmission("form-selector") - Test form submission');
        console.log('- formDebug.clearLogs() - Clear debug logs');
        
        // Check for common issues
        const forms = document.querySelectorAll('form');
        console.log(`Found ${forms.length} forms on page`);
        
        forms.forEach((form, index) => {
            const formId = form.id || `form-${index}`;
            const hasAction = form.action && form.action !== window.location.href;
            const hasMethod = form.method && form.method.toLowerCase() !== 'get';
            
            if (!hasAction) {
                console.warn(`Form ${formId} has no action attribute or submits to current page`);
            }
            
            if (!hasMethod) {
                console.warn(`Form ${formId} uses GET method or no method specified`);
            }
        });
    });
    
})();
