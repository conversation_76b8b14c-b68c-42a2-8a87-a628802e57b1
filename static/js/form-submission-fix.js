/**
 * Form Submission Fix
 * Ensures proper form submission behavior across the application
 */

(function() {
    'use strict';
    
    // Track forms that should use AJAX vs normal submission
    const ajaxForms = new Set([
        'showNoteForm',
        'resolveForm',
        'quickCreateForm',
        'conflictResolutionForm'
    ]);
    
    // Track forms that should use normal submission
    const normalForms = new Set([
        'client-form',
        'presenter-form',
        'mention-form',
        'industry-form',
        'show-form'
    ]);
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Form Submission Fix initialized');
        
        // Fix forms that should use normal submission
        fixNormalSubmissionForms();
        
        // Enhance AJAX forms with better error handling
        enhanceAjaxForms();
        
        // Add global form submission monitoring
        addGlobalFormMonitoring();
    });
    
    /**
     * Fix forms that should use normal submission but might be prevented
     */
    function fixNormalSubmissionForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const formId = form.id;
            const formClass = form.className;
            
            // Skip forms that should use AJAX
            if (shouldUseAjax(form)) {
                return;
            }
            
            // Ensure normal forms can submit properly
            if (shouldUseNormalSubmission(form)) {
                ensureNormalSubmission(form);
            }
        });
    }
    
    /**
     * Check if form should use AJAX
     */
    function shouldUseAjax(form) {
        const formId = form.id;
        const formClass = form.className;
        
        // Check explicit AJAX forms
        if (ajaxForms.has(formId)) {
            return true;
        }
        
        // Check for AJAX indicators
        if (form.hasAttribute('data-ajax') || 
            formClass.includes('ajax-form') ||
            formId.includes('modal') ||
            formId.includes('quick')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if form should use normal submission
     */
    function shouldUseNormalSubmission(form) {
        const formId = form.id;
        const formClass = form.className;
        
        // Check explicit normal forms
        if (normalForms.has(formId)) {
            return true;
        }
        
        // Check for normal form indicators
        if (form.action && form.action !== window.location.href) {
            return true;
        }
        
        // Forms with method POST that aren't explicitly AJAX
        if (form.method && form.method.toLowerCase() === 'post' && !shouldUseAjax(form)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Ensure form can submit normally
     */
    function ensureNormalSubmission(form) {
        console.log('Ensuring normal submission for form:', form.id || form.className);
        
        // Remove any existing submit handlers that might prevent submission
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
        
        // Add basic validation that doesn't prevent submission
        newForm.addEventListener('submit', function(e) {
            // Only prevent if there are actual validation errors
            const requiredFields = this.querySelectorAll('[required]');
            let hasErrors = false;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    hasErrors = true;
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            if (hasErrors) {
                e.preventDefault();
                console.log('Form submission prevented due to validation errors');
                return false;
            }
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Submitting...';
                
                // Reset button after timeout (fallback)
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                }, 10000);
            }
            
            console.log('Form submission allowed - processing normally');
            return true;
        });
    }
    
    /**
     * Enhance AJAX forms with better error handling
     */
    function enhanceAjaxForms() {
        // This is handled by individual form scripts
        // Just ensure they have proper error handling
        console.log('AJAX forms enhanced');
    }
    
    /**
     * Add global form submission monitoring
     */
    function addGlobalFormMonitoring() {
        document.addEventListener('submit', function(e) {
            const form = e.target;
            
            // Log form submission attempts
            console.log('Form submission attempt:', {
                id: form.id,
                action: form.action,
                method: form.method,
                prevented: e.defaultPrevented
            });
            
            // Check for common issues
            if (e.defaultPrevented) {
                console.warn('Form submission was prevented for form:', form.id || form.className);
                
                // Try to identify why it was prevented
                setTimeout(() => {
                    if (window.formDebug) {
                        console.log('Use formDebug.checkForm() to analyze this form');
                    }
                }, 100);
            }
        }, true);
    }
    
    /**
     * Utility function to show notifications
     */
    function showNotification(message, type = 'info') {
        if (window.RadioMention && window.RadioMention.showNotification) {
            window.RadioMention.showNotification(message, type);
        } else if (window.notificationSystem) {
            window.notificationSystem.show(message, type, 3000);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    // Export for debugging
    window.formSubmissionFix = {
        shouldUseAjax,
        shouldUseNormalSubmission,
        ensureNormalSubmission,
        showNotification
    };
    
})();
