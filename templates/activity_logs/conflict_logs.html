{% extends 'base.html' %}
{% load static %}

{% block title %}Conflict Detection{% endblock %}

{% block extra_css %}
<style>
.conflict-warning {
    background-color: #fef2f2;
    border-color: #fecaca;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.severity-high {
    border-left: 4px solid #ef4444;
}

.severity-medium {
    border-left: 4px solid #f59e0b;
}

.severity-low {
    border-left: 4px solid #10b981;
}

.status-detected {
    background-color: #fef2f2;
    color: #991b1b;
}

.status-resolved {
    background-color: #f0fdf4;
    color: #166534;
}

.status-acknowledged {
    background-color: #fef3c7;
    color: #92400e;
}

.status-ignored {
    background-color: #f3f4f6;
    color: #374151;
}
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">Conflict Detection</h1>
                    <div class="ml-4 text-sm text-gray-500">
                        Monitor and resolve scheduling conflicts
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="detectConflicts()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 flex items-center">
                        <i class="fa-solid fa-search mr-2"></i>
                        Detect Conflicts
                    </button>
                    <button class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 flex items-center">
                        <i class="fa-solid fa-download mr-2"></i>
                        Export Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Conflict Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Total Conflicts</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.total_conflicts }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Unresolved</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.unresolved_conflicts }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Resolved</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.resolved_conflicts }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-fire text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">High Severity</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.high_severity }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Conflict Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Filter Conflicts</h3>
            <a href="{% url 'activity_logs:conflict_logs' %}" class="text-sm text-primary-600 hover:text-primary-700">Clear all filters</a>
        </div>
        <form method="GET" class="flex flex-wrap items-center gap-3">
            <div class="flex items-center gap-2">
                <label class="text-sm font-medium text-gray-700">Status:</label>
                <select name="status" class="w-36 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Status</option>
                    <option value="detected" {% if filters.status == 'detected' %}selected{% endif %}>Detected</option>
                    <option value="acknowledged" {% if filters.status == 'acknowledged' %}selected{% endif %}>Acknowledged</option>
                    <option value="resolved" {% if filters.status == 'resolved' %}selected{% endif %}>Resolved</option>
                    <option value="ignored" {% if filters.status == 'ignored' %}selected{% endif %}>Ignored</option>
                </select>
            </div>
            <div class="flex items-center gap-2">
                <label class="text-sm font-medium text-gray-700">Type:</label>
                <select name="type" class="w-52 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Types</option>
                    <option value="time_overlap" {% if filters.type == 'time_overlap' %}selected{% endif %}>Time Overlap</option>
                    <option value="presenter_double_booking" {% if filters.type == 'presenter_double_booking' %}selected{% endif %}>Presenter Double Booking</option>
                    <option value="show_capacity_exceeded" {% if filters.type == 'show_capacity_exceeded' %}selected{% endif %}>Show Capacity Exceeded</option>
                </select>
            </div>
            <div class="flex items-center gap-2">
                <label class="text-sm font-medium text-gray-700">Severity:</label>
                <select name="severity" class="w-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Severity</option>
                    <option value="high" {% if filters.severity == 'high' %}selected{% endif %}>High</option>
                    <option value="medium" {% if filters.severity == 'medium' %}selected{% endif %}>Medium</option>
                    <option value="low" {% if filters.severity == 'low' %}selected{% endif %}>Low</option>
                </select>
            </div>
            <div class="ml-4">
                <button type="submit" class="px-3 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Conflicts List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Detected Conflicts</h3>
        </div>
        <div class="divide-y divide-gray-200">
            {% if page_obj %}
                {% for conflict in page_obj %}
                    <div class="p-6 severity-{{ conflict.severity }}">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-{{ conflict.status }}">
                                        {{ conflict.get_status_display }}
                                    </span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ conflict.get_severity_display }} Severity
                                    </span>
                                    <span class="ml-2 text-sm font-medium text-gray-900">{{ conflict.get_conflict_type_display }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">{{ conflict.description }}</p>
                                
                                <!-- Conflicting Mentions -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                    <div class="bg-red-50 border border-red-200 rounded-md p-3">
                                        <h4 class="text-sm font-medium text-red-800 mb-1">{{ conflict.mention1.title }}</h4>
                                        <p class="text-xs text-red-600">{{ conflict.mention1.client.name }}</p>
                                        <p class="text-xs text-red-600">Priority: {{ conflict.mention1.get_priority_display }}</p>
                                    </div>
                                    {% if conflict.mention2 and conflict.mention2 != conflict.mention1 %}
                                        <div class="bg-red-50 border border-red-200 rounded-md p-3">
                                            <h4 class="text-sm font-medium text-red-800 mb-1">{{ conflict.mention2.title }}</h4>
                                            <p class="text-xs text-red-600">{{ conflict.mention2.client.name }}</p>
                                            <p class="text-xs text-red-600">Priority: {{ conflict.mention2.get_priority_display }}</p>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="flex items-center text-xs text-gray-500">
                                    <span>Detected {{ conflict.created_at|timesince }} ago</span>
                                    {% if conflict.resolved_by %}
                                        <span class="mx-2">•</span>
                                        <span>Resolved by {{ conflict.resolved_by.get_full_name|default:conflict.resolved_by.username }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="ml-4 flex flex-col space-y-2">
                                {% if conflict.status == 'detected' %}
                                    <button onclick="resolveConflict({{ conflict.pk }}, 'priority')" 
                                            class="px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700">
                                        Auto Resolve
                                    </button>
                                    <button onclick="showResolveModal({{ conflict.pk }})" 
                                            class="px-3 py-1 bg-green-600 text-white text-xs font-medium rounded-md hover:bg-green-700">
                                        Manual Resolve
                                    </button>
                                    <button onclick="acknowledgeConflict({{ conflict.pk }})" 
                                            class="px-3 py-1 bg-yellow-600 text-white text-xs font-medium rounded-md hover:bg-yellow-700">
                                        Acknowledge
                                    </button>
                                {% endif %}
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fa-solid fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        
                        {% if conflict.resolution_notes %}
                            <div class="mt-3 p-3 bg-gray-50 rounded-md">
                                <p class="text-sm text-gray-700"><strong>Resolution Notes:</strong> {{ conflict.resolution_notes }}</p>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                        <div class="text-sm text-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} conflicts
                        </div>
                        <div class="flex space-x-2">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</a>
                            {% endif %}
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <div class="p-12 text-center">
                    <i class="fa-solid fa-check-circle text-green-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No conflicts detected</h3>
                    <p class="text-gray-500">All mentions are properly scheduled without conflicts.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Resolution Modal -->
<div id="resolveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Resolve Conflict</h3>
            <form id="resolveForm">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Resolution Strategy</label>
                    <select name="strategy" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="manual">Manual Resolution</option>
                        <option value="priority">By Priority</option>
                        <option value="first_come">First Come First Serve</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Resolution Notes</label>
                    <textarea name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Enter resolution notes..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeResolveModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        Resolve
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentConflictId = null;

function detectConflicts() {
    fetch('{% url "activity_logs:detect_conflicts" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    });
}

function resolveConflict(conflictId, strategy) {
    const formData = new FormData();
    formData.append('strategy', strategy);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    fetch(`/activity/conflicts/${conflictId}/resolve/`, {
        method: 'POST',
        body: formData,
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    });
}

function showResolveModal(conflictId) {
    currentConflictId = conflictId;
    document.getElementById('resolveModal').classList.remove('hidden');
}

function closeResolveModal() {
    document.getElementById('resolveModal').classList.add('hidden');
    currentConflictId = null;
}

document.getElementById('resolveForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    // Show loading state
    const submitButton = this.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Resolving...';

    fetch(`/activity/conflicts/${currentConflictId}/resolve/`, {
        method: 'POST',
        body: formData,
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Use global notification system if available
            if (window.RadioMention && window.RadioMention.showNotification) {
                window.RadioMention.showNotification(data.message, 'success');
            } else {
                alert(data.message);
            }

            // Reload page after a short delay to show the notification
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            // Use global notification system if available
            if (window.RadioMention && window.RadioMention.showNotification) {
                window.RadioMention.showNotification('Error: ' + data.error, 'error');
            } else {
                alert('Error: ' + data.error);
            }

            // Restore button state
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('Error resolving conflict:', error);

        // Use global notification system if available
        if (window.RadioMention && window.RadioMention.showNotification) {
            window.RadioMention.showNotification('Failed to resolve conflict. Please try again.', 'error');
        } else {
            alert('Failed to resolve conflict. Please try again.');
        }

        // Restore button state
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
});
</script>
{% endblock %}
