#!/usr/bin/env python
"""
Fix the pending mentions for Radio City organization that should be auto-approved.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')
django.setup()

from django.utils import timezone
from apps.mentions.models import Mention
from apps.settings.models import OrganizationSettings

def fix_radio_city_pending_mentions():
    """Fix pending mentions for Radio City that should be auto-approved."""
    print("=== Fixing Radio City Pending Mentions ===")
    
    # Find Radio City organization
    try:
        from apps.organizations.models import Organization
        radio_city = Organization.objects.get(name="Radio City")
        print(f"Found Radio City organization (ID: {radio_city.id})")
    except Organization.DoesNotExist:
        print("❌ Radio City organization not found")
        return
    
    # Check organization settings
    try:
        org_settings = OrganizationSettings.objects.get(organization=radio_city)
        print(f"Organization settings:")
        print(f"  - require_mention_approval: {org_settings.require_mention_approval}")
        print(f"  - auto_approve_recurring: {org_settings.auto_approve_recurring}")
    except OrganizationSettings.DoesNotExist:
        print("❌ No organization settings found for Radio City")
        return
    
    # Find pending mentions for Radio City
    pending_mentions = Mention.objects.filter(
        client__organization=radio_city,
        status='pending'
    ).select_related('client', 'recurring_mention', 'created_by')
    
    print(f"Found {pending_mentions.count()} pending mentions for Radio City")
    
    if pending_mentions.count() == 0:
        print("✅ No pending mentions found")
        return
    
    approved_count = 0
    
    for mention in pending_mentions:
        print(f"Processing: '{mention.title[:50]}...'")
        print(f"  - Recurring: {'Yes' if mention.recurring_mention else 'No'}")
        print(f"  - Created by: {mention.created_by}")
        print(f"  - Created at: {mention.created_at}")
        
        # Determine if this should be auto-approved
        should_approve = False
        reason = ""
        
        if not org_settings.require_mention_approval:
            should_approve = True
            reason = "approval not required for organization"
        elif mention.recurring_mention and org_settings.auto_approve_recurring:
            should_approve = True
            reason = "auto-approve recurring enabled"
        
        if should_approve:
            # Apply approval
            mention.status = 'scheduled'
            mention.approved_by = mention.created_by
            mention.approved_at = timezone.now()
            mention.save()
            
            print(f"  ✅ APPROVED - {reason}")
            approved_count += 1
        else:
            print(f"  ⚠️  SKIPPED - requires manual approval")
    
    print(f"\n=== Summary ===")
    print(f"✅ Approved: {approved_count} mentions")
    print(f"📊 Total processed: {pending_mentions.count()} mentions")

def test_mention_save_logic():
    """Test the Mention.save() logic with a sample mention."""
    print("\n=== Testing Mention Save Logic ===")
    
    # Get Radio City organization
    try:
        from apps.organizations.models import Organization
        from apps.core.models import Client
        
        radio_city = Organization.objects.get(name="Radio City")
        client = Client.objects.filter(organization=radio_city).first()
        
        if not client:
            print("❌ No client found for Radio City")
            return
            
        print(f"Using client: {client.name}")
        
        # Get organization settings
        org_settings = OrganizationSettings.objects.get(organization=radio_city)
        print(f"Organization settings:")
        print(f"  - require_mention_approval: {org_settings.require_mention_approval}")
        print(f"  - auto_approve_recurring: {org_settings.auto_approve_recurring}")
        
        # Test what happens when we create a new mention
        print(f"\nTesting auto-approval logic:")
        print(f"  - require_mention_approval = {org_settings.require_mention_approval}")
        
        if not org_settings.require_mention_approval:
            print("  ✅ Should auto-approve: approval not required")
        elif org_settings.auto_approve_recurring:
            print("  ✅ Should auto-approve recurring mentions")
        else:
            print("  ⚠️  Should require manual approval")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_radio_city_pending_mentions()
    test_mention_save_logic()
