#!/usr/bin/env python
"""
Script to fix pending recurring mentions and test auto-approval functionality.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')
django.setup()

from django.utils import timezone
from apps.mentions.models import Mention, RecurringMention, RecurringMentionShow
from apps.settings.models import OrganizationSettings
from apps.organizations.models import Organization
from apps.core.models import Client
from apps.shows.models import Show
from django.contrib.auth.models import User
from datetime import date, time, timedelta

def fix_pending_recurring_mentions():
    """Fix existing pending recurring mentions that should be auto-approved."""
    print("=== Fixing Pending Recurring Mentions ===")
    
    # Find all pending recurring mentions
    pending_mentions = Mention.objects.filter(
        status='pending',
        recurring_mention__isnull=False
    ).select_related('client__organization', 'recurring_mention', 'created_by')
    
    total_mentions = pending_mentions.count()
    print(f"Found {total_mentions} pending recurring mentions")
    
    if total_mentions == 0:
        print("No pending recurring mentions found.")
        return
    
    approved_count = 0
    skipped_count = 0
    
    for mention in pending_mentions:
        try:
            org = mention.client.organization
            
            # Get organization settings
            try:
                org_settings = OrganizationSettings.objects.get(organization=org)
            except OrganizationSettings.DoesNotExist:
                print(f"Skipping mention - No organization settings for {org.name}")
                skipped_count += 1
                continue
            
            # Check if this mention should be auto-approved
            should_approve = False
            reason = ""
            
            if not org_settings.require_mention_approval:
                should_approve = True
                reason = "approval not required"
            elif org_settings.auto_approve_recurring:
                should_approve = True
                reason = "auto-approve recurring enabled"
            
            if should_approve:
                # Apply approval
                mention.status = 'scheduled'
                mention.approved_by = mention.created_by
                mention.approved_at = timezone.now()
                mention.save()
                
                print(f"✅ Approved: \"{mention.title[:50]}...\" ({org.name}) - {reason}")
                approved_count += 1
            else:
                print(f"Skipping: \"{mention.title[:50]}...\" ({org.name}) - requires manual approval")
                skipped_count += 1
                
        except Exception as e:
            print(f"Error processing mention \"{mention.title[:50]}...\": {str(e)}")
    
    print(f"\n=== Summary ===")
    print(f"Approved: {approved_count} mentions")
    print(f"Skipped: {skipped_count} mentions")

def test_new_recurring_mention():
    """Test creating a new recurring mention to verify auto-approval works."""
    print("\n=== Testing New Recurring Mention Auto-Approval ===")
    
    # Get Radio City organization (which has auto-approval enabled)
    try:
        org = Organization.objects.get(name="Radio City")
        settings = OrganizationSettings.objects.get(organization=org)
        
        print(f"Organization: {org.name}")
        print(f"require_mention_approval: {settings.require_mention_approval}")
        print(f"auto_approve_recurring: {settings.auto_approve_recurring}")
        
        # Get required objects
        client = Client.objects.filter(organization=org).first()
        show = Show.objects.filter(organization=org).first()
        user = User.objects.first()
        
        if not all([client, show, user]):
            print("Missing required objects (client, show, or user)")
            return
        
        print(f"Using client: {client.name}")
        print(f"Using show: {show.name}")
        
        # Create a test recurring mention
        recurring_mention = RecurringMention.objects.create(
            title='Auto-Approval Test',
            content='This should be automatically approved',
            client=client,
            frequency='weekly',
            weekdays=[0, 1, 2, 3, 4],  # Monday to Friday
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            created_by=user
        )
        
        print(f"Created recurring mention: {recurring_mention.title} (ID: {recurring_mention.id})")
        
        # Create show assignment (this should trigger automatic mention generation via signal)
        show_assignment = RecurringMentionShow.objects.create(
            recurring_mention=recurring_mention,
            show=show,
            scheduled_time=time(10, 30),
            scheduled_days=[0, 1, 2, 3, 4]
        )
        
        print(f"Created show assignment: {show.name} at {show_assignment.scheduled_time}")
        
        # Check the generated mentions
        generated_mentions = Mention.objects.filter(recurring_mention=recurring_mention)
        print(f"Generated {generated_mentions.count()} individual mentions")
        
        if generated_mentions.exists():
            approved_count = generated_mentions.filter(status='scheduled').count()
            pending_count = generated_mentions.filter(status='pending').count()
            
            print(f"  Approved (scheduled): {approved_count}")
            print(f"  Pending: {pending_count}")
            
            first_mention = generated_mentions.first()
            print(f"First mention status: {first_mention.status}")
            print(f"First mention approved_by: {first_mention.approved_by}")
            print(f"First mention approved_at: {first_mention.approved_at}")
            
            if first_mention.status == 'scheduled':
                print("✅ SUCCESS: New mentions were automatically approved!")
            else:
                print("❌ ISSUE: New mentions were not automatically approved")
        else:
            print("❌ ISSUE: No mentions were generated")
            
    except Organization.DoesNotExist:
        print("Radio City organization not found")
    except Exception as e:
        print(f"Error testing new recurring mention: {str(e)}")

if __name__ == "__main__":
    # Fix existing pending mentions
    fix_pending_recurring_mentions()
    
    # Test new recurring mention creation
    test_new_recurring_mention()
