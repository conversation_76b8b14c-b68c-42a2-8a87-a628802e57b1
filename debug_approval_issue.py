#!/usr/bin/env python
"""
Debug script to investigate why new recurring mentions are not being auto-approved.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')
django.setup()

from django.utils import timezone
from apps.mentions.models import RecurringMention, Mention
from apps.settings.models import OrganizationSettings
from apps.organizations.models import Organization

def debug_recent_recurring_mentions():
    """Debug the most recent recurring mentions and their approval status."""
    print("=== Debugging Recent Recurring Mentions ===")
    
    # Get the most recent recurring mentions (last 10)
    recent_recurring = RecurringMention.objects.order_by('-created_at')[:10]
    
    if not recent_recurring:
        print("❌ No recurring mentions found")
        return
    
    print(f"Found {len(recent_recurring)} recent recurring mentions:")
    print()
    
    for i, recurring in enumerate(recent_recurring, 1):
        print(f"--- Recurring Mention #{i} ---")
        print(f"ID: {recurring.id}")
        print(f"Title: {recurring.title}")
        print(f"Client: {recurring.client.name}")
        print(f"Organization: {recurring.client.organization.name}")
        print(f"Created: {recurring.created_at}")
        print(f"Status: {recurring.status}")
        print(f"Created by: {recurring.created_by}")
        
        # Check organization settings
        try:
            org_settings = OrganizationSettings.objects.get(organization=recurring.client.organization)
            print(f"Organization Settings:")
            print(f"  - require_mention_approval: {org_settings.require_mention_approval}")
            print(f"  - auto_approve_recurring: {org_settings.auto_approve_recurring}")
        except OrganizationSettings.DoesNotExist:
            print("❌ NO ORGANIZATION SETTINGS FOUND!")
        
        # Check generated individual mentions
        individual_mentions = Mention.objects.filter(recurring_mention=recurring)
        print(f"Generated individual mentions: {individual_mentions.count()}")
        
        if individual_mentions.count() > 0:
            approved_count = individual_mentions.filter(status='scheduled').count()
            pending_count = individual_mentions.filter(status='pending').count()
            
            print(f"  - Approved (scheduled): {approved_count}")
            print(f"  - Pending: {pending_count}")
            
            # Show details of first few mentions
            for j, mention in enumerate(individual_mentions[:3], 1):
                print(f"    Mention {j}: '{mention.title[:30]}...' - Status: {mention.status}")
                if mention.status == 'scheduled':
                    print(f"      ✅ Approved by: {mention.approved_by} at {mention.approved_at}")
                else:
                    print(f"      ⚠️  Still pending approval")
        else:
            print("  - No individual mentions generated yet")
        
        # Check show assignments
        show_assignments = recurring.recurringmentionshow_set.all()
        print(f"Show assignments: {show_assignments.count()}")
        for assignment in show_assignments:
            print(f"  - {assignment.show.name} at {assignment.scheduled_time} (days: {assignment.scheduled_days})")
        
        print()

def check_all_organization_settings():
    """Check all organizations and their settings."""
    print("=== All Organization Settings ===")
    
    orgs = Organization.objects.filter(is_active=True)
    
    for org in orgs:
        try:
            settings = OrganizationSettings.objects.get(organization=org)
            print(f"✅ {org.name}:")
            print(f"   require_mention_approval: {settings.require_mention_approval}")
            print(f"   auto_approve_recurring: {settings.auto_approve_recurring}")
        except OrganizationSettings.DoesNotExist:
            print(f"❌ {org.name}: NO SETTINGS")
    
    print()

def check_pending_mentions():
    """Check current pending mentions."""
    print("=== Current Pending Mentions ===")
    
    pending_mentions = Mention.objects.filter(status='pending').order_by('-created_at')[:10]
    
    if not pending_mentions:
        print("✅ No pending mentions found")
        return
    
    print(f"Found {pending_mentions.count()} pending mentions:")
    
    for mention in pending_mentions:
        print(f"- '{mention.title[:50]}...' ({mention.client.organization.name})")
        print(f"  Created: {mention.created_at}")
        print(f"  Recurring: {'Yes' if mention.recurring_mention else 'No'}")
        if mention.recurring_mention:
            print(f"  Recurring ID: {mention.recurring_mention.id}")

if __name__ == "__main__":
    debug_recent_recurring_mentions()
    check_all_organization_settings()
    check_pending_mentions()
